Content-based Analysis of the Cultural Differences
 between TikTok and <PERSON><PERSON><PERSON>*
 Georgen Institute for Data Science
 University of Rochester
 Rochester, United States
 <EMAIL>
 Songyang Zhang
 arXiv:2011.01414v1  [cs.CV]  3 Nov 2020
 Department of Computer Science
 University of Rochester
 Rochester, United States
 <EMAIL>
 Abstract—Short-form video social media shifts away from the
 traditional media paradigm by telling the audience a dynamic
 story to attract their attention. In particular, different combina
tions of everyday objects can be employed to represent a unique
 scene that is both interesting and understandable. Offered by
 the same company, TikTok and Douyin are popular examples
 of such new media that has become popular in recent years,
 while being tailored for different markets (e.g. the United States
 and China). The hypothesis that they express cultural differences
 together with media fashion and social idiosyncrasy is the
 primary target of our research. To that end, we first employ the
 Faster Regional Convolutional Neural Network (Faster R-CNN)
 pre-trained with the Microsoft Common Objects in COntext
 (MS-COCO) dataset to perform object detection. Based on a
 suite of objects detected from videos, we perform statistical
 analysis including label statistics, label similarity, and label
person distribution. We further use the Two-Stream Inflated 3D
 ConvNet (I3D) pre-trained with the Kinetics dataset to categorize
 and analyze human actions. By comparing the distributional
 results of <PERSON>ik<PERSON><PERSON> and <PERSON><PERSON><PERSON>, we uncover a wealth of similarity
 and contrast between the two closely related video social media
 platforms along the content dimensions of object quantity, object
 categories, and human action categories.
 Index Terms—TikTok, Douyin, Social media, Social video,
 Cultural difference
 I. INTRODUCTION
 Online social networks often reflect a wide variety of
 lifestyles through the shared contents from users. Recently,
 video-sharing social networks are becoming increasingly pop
ular due to the advances in internet and video capture tech
nologies. This new form of social media enables users to share
 their interests and daily lives more effectively by carefully
 composing short videos for maximum effect. The content of
 videos, for example, the main objects, the background, and
 the actions in turn would provide an accurate representation
 of the culture, fashion, and value within a specific geographic
 *Both authors contributed equally on this paper.
 Haoqi Zhang*
 Department of Computer Science
 University of Rochester
 Rochester, United States
 <EMAIL>
 Jiebo Luo
 Department of Computer Science
 University of Rochester
 Rochester, United States
 <EMAIL>
 region. Therefore, videos from various locations are expected
 to exhibit different cultural characteristics.
 Our study aims at understanding the cultural differences be
tween TikTok and Douyin because these two applications offer
 similar functions but for different markets. Both applications
 have a 15-second default length and the restriction can be
 relaxed to a maximum of 5 minutes with special applications
 or invitations. They also concentrate on dynamic expressions
 through lively actions, creative challenges, and so on. The
 target users in Douyin are primarily from China, especially the
 youth between 15 to 25 years old. TikTok is the international
 version of Douyin, as a separate system available in the app
 store for people outside China. Although they are prevalent in
 many countries, our study only focuses on investigating the
 cultural differences in the United States and China.
 In this study, cultural preference is investigated by con
ducting object label analysis and human action analysis. For
 object label analysis, we categorize the labels computed by
 Faster R-CNN [1] and analyze in terms of three aspects: label
 statistics, label similarity, and label-person distribution. Figure
 1 shows the label frequency distribution. The set of selected
 labels together describes the main content of the video, and the
 collection of main contents indicates the characteristics of a
 culture. Furthermore, we apply I3D [2] to detect video actions
 and categorize them in a similar manner (Figure 2).
 First, we summarize and compare the basic statistics for
 the frequency of various labels in TikTok and Douyin. Based
 on the quantity and variance of labels, we find that although
 some labels possess similar distributional patterns, differences
 among high-frequency labels exist as the reflection of the
 cultural preference in each group.
 Next, we use the label similarity to measure cultural di
versity by examining the difference across labels. The sum
 of label differences can be represented in a similarity matrix
 using the Cosine similarity. It enables us to investigate the
 diversity both within and across categories.
(a) Douyin
 (a) Douyin
 Category
 Fig. 2. Tag clouds of human actions.
 (b) TikTok
 Fig. 1. Tag clouds of object labels.
 (b) TikTok
 Labels
 Person
 Person
 Vehicle
 Bicycle, Car, Motorcycle, Airplane, Bus, Train, Truck, Boat
 Outdoor
 Traffic Light, Fire Hydrant, Stop Sign, Parking Meter, Bench
 Animal
 Bird, Cat, Dog, Horse, Sheep, Cow, Elephant, Bear, Zebra, Giraffe
 Accessory
 Backpack, Umbrella, Handbag, Tie, Suitcase
 Sports
 Frisbee, Skis, Snowboard, Sports Ball, Kite, Baseball Bat, Baseball Glove, Skateboard, Surfboard,
 Tennis Racket
 Tableware
 Bottle, Wine Glass, Cup, Fork, Knife, Spoon, Bowl
 Food
 Banana, Apple, Sandwich, Orange, Broccoli, Carrot, Hot Dog, Pizza, Donut, Cake
 Furniture
 Chair, Couch, Potted Plant, Bed, Dining Table, Toilet
 Electronic
 TV, Laptop, Mouse, Remote, Keyboard, Cell Phone, Microwave, Oven, Sink, Refrigerator
 Indoor
 Book, Clock, Vase, Scissors, Teddy Bear, Hair Drier, Toothbrush
 TABLE I
 TAXONOMY FOR LABEL ANALYSIS.
 Furthermore, we focus on the average frequency distribution
 of person condition on each label, because person is the most
 frequent and important label. There is an intimate cause-effect
 relation between the count of person and the appearance of
 different objects, and the collection of those relations reflects
 the cultural difference.
 Finally, since social videos are often centered around human
 actions, automatic human action analysis is performed to
 observe the dynamic aspects of cultural difference. With the
 help of frequency statistics, we discover that users in different
 countries and cultures tend to have different expression habits
 and behave differently.
 The significance of our research about TikTok and Douyin
 stems from the extremely high popularity of these two applica
tions with similar media forms but strikingly different primary
 markets in the United State and China, respectively. Given
 the offering of two market-tailored versions, each market
 is independent and thus relatively closed with respect to
 the content of the video stories. Therefore, when the two
 applications grow in different regions, will the content reflect
Fig. 3. Taxonomy for human action analysis.
 the local conditions? Content shifts according to users’ unique
 habits and values, which are shaped by different cultures and
 backgrounds. With such inspiration in mind, we formulate
 the following research questions: (1) Does the distributions
 of video labels reflect cultural differences? (2) How different
 are the distributions across labels? (3) What is the correlation
 between person and other labels? and (4) Do users with
 different cultural backgrounds behave differently?
 II. RELATED WORK
 Many recent studies in the emerging computational social
 science deviate from the traditional research by directly an
alyzing the visual content and the associate cultural factors
 through automated content analysis. Furthermore, the early
 published works related to short video data mining primarily
 focused on video metadata indirectly rather than video content
 directly. Therefore, our approach breaks these two traditional
 paradigms through the parallel use of large-scale quantitative
 analysis and automated visual content analysis on different
 cultural subjects and backgrounds.
 Our social media analysis is video-based, which is closely
 related to the work by Zhang et al. on label-based analysis
 using a similarity matrix and object distributions by region
 [3]. Rather than using only the video thumbnails, our method
 is facilitated by understanding the full content of the video
 collections using the state-of-the-art computer vision tech
niques described by Jha et al. [4]. Other related research also
 adopted similar technology, such as anorexia on YouTube [5],
 background modeling [6], lingual orthodontic treatment [7],
 and video object segmentation [8].
 In a related image-based cultural study, You et al. uncovered
 spatiotemporal trends [9]. Image formation technology was
 used to measure tourism information quality on Sina Weibo
 [10]. Amato et al. implemented a convolutional neural network
 (CNN) to perform social media food image recognition on
 Twitter [11]. CNN is a specific type of Deep Neural Networks
 (DNN) in deep learning [12]. Another research by Pittman
 and Reich is oriented toward studying loneliness, where they
 compared image-based and text-based social media platforms
 [13]. Lai et al. also used image data to predict the personality
 traits, needs, and values of social media users [14].
 The existing research about Douyin was mainly concerned
 with users’ motivation, expectation, and behavior, including
 user interface analysis [15] and online network sampling [16],
 which combined TikTok and Douyin as a whole. In contrast,
 our work aims to investigate the unique territory of cross
culture comparison as far as social media video is concerned,
 taking advantage of the natural separation created by having
 two different versions of essentially the same social video
 sharing application.
Indoor 
Indoor 
Electronic 
Furniture 
Food 
Tableware 
Sports 
Accessory 
Animal 
Outdoor 
Vehicle 
Electronic 
Furniture 
Food 
Tableware 
Sports 
Accessory 
Animal 
Outdoor 
Vehicle 
Log of Frequency 
Average Frequency per Video 
Fig. 4. Label frequency, mean, and range comparison for Douyin and TikTok (without person).
 III. DATA
 We collect video data by randomly downloading 5,000
 videos for TikTok and 5,000 videos for Douyin. To ensure
 that the sampling process is sufficiently random, videos are
 downloaded directly from the “trending” section without sign
ing into the application (thus free from the filtering and
 recommendation for each user). Different videos have different
 lengths, and thus are composed of different numbers of frames.
 Each second contains 30 frames. For each video, we capture
 one frame every five seconds. In total, there are 16,063 frames
 for TikTok and 15,217 frames for Douyin. Since the target
 market of Douyin is China, most downloaded Douyin videos
 come from China. In contrast, TikTok targets the entire world.
 In this study, we only focus on the videos from the United
 States by restricting the IP address.
 IV. METHODOLOGY
 In contrast to prior work, we are interested in understanding
 the content difference from captured video frames. To evaluate
 the objects in frames, we first implement Faster R-CNN
 [1] and pre-train it with the MS-COCO dataset. MS-COCO
 collects labeled common objects in daily scenes to promote
 precise object localization [17]. Faster R-CNN predicts a list
 of predefined objects and provides a confidence score for each
 object. The higher the score is, the more likely the prediction
 is correct. We set the accepting threshold to 0.85 (every object
 below this threshold is ignored) to strike a balance between
(a) Douyin
 (a) Douyin
 Fig. 6. Cosine similarity for the food category.
 information accuracy and data richness. In other words, every
 object above 0.85 will be counted as 1 and otherwise as
 0. Next, to normalize the data, we divide the total object
 frequency by the number of frames.
 There are 92 labels [17] in the MS-COCO dataset, as shown
 in Table 1. We filter out labels that are not detected for both
 Douyin and TikTok and retain 80 labels. We empirically group
 the labels into 11 categories [17]. First, we compare the label
 frequency both within and across categories. Since different
 labels exhibit high frequency differences, we take the log to
 normalize the data. Label mean and range are also compared
 by the categories in Table 1. Second, we create similarity
 matrices calculated by the Cosine similarity to illustrate the
 label differences. Finally, for each label, we calculate the
 average person frequency and analyze the distribution.
 For videos that contain person, we also implement the I3D
 (b) TikTok
 Fig. 5. Cosine similarity for the animal category.
 (b) TikTok
 algorithm [2] to analyze human actions. The algorithm outputs
 a score for each action to represent the occurrence probability
 for every 0.64 seconds. We use the average probability of all
 video frames as the video prediction. If an action has a prob
ability larger than 0.04 (empirically determined), we regard
 the video as containing such an action. There are 400 actions
 (Figure 1) in the Kinetics dataset [18]. We use the hierarchical
 taxonomy to separate actions into 11 categories and 3 super
categories. We then analyze the frequency distribution of these
 categories and super-categories for TikTok and Douyin.
 V. ANALYSIS RESULTS
 A. Object Label Analysis
 1) Label Statistics: In general, we analyze the label char
acteristics by the normalized frequency shown on the left
 of Figure 4. We find some patterns in the categories. First,
(a) Douyin
 for all outdoor labels, TikTok dominates Douyin. It indicates
 TikTok users like to shoot videos outdoors. For most labels
 in tableware, Douyin leads in their frequencies. However,
 TikTok leads in electronics. This shows that a Douyin user’s
 life is more mundane, in contrast to TikTok which is more
 technological. There is a mix in animal, with more livestock
 in Douyin but more pets in TikTok. Such animal related
 differences could represent unique cultural characteristics in
 a specific region.
 Next, we examine the average frequency of labels (the
 appearance frequency in each video divided by the number
 of captured frames) as shown on the right of Figure 4 which
 shows more fine-grained comparative statistics. We discover
 that the mean is relatively stable, but the range varies dramat
ically. This implies that label distributions are skewed with
 outliers on the high-frequency side. Most means are smaller
 than 1.0 since they are computed for each label in each video
 and every frame we take includes that label. We first analyze
 bicycle and boat that may represent a certain lifestyle. Bicycle
 in TikTok has a range far greater than that in Douyin but for
 boat this is slightly reversed. It implies that they have different
 vehicle preferences during commuting or exercise.
 Other interesting labels are worth further discussion. For
 example, cell phone and book show higher variance in TikTok
 than Douyin, despite little difference in the mean. Perhaps
 people in Douyin use them for multiple purposes, such as
 game watching, story telling, or exhibition.
 2) Label Similarity: We analyze the relationship between
 labels and select several representative categories. Such re
lationships are captured by the similarity matrices with the
 Cosine Similarity shown in Figures 6 to 8. Note that we focus
 more on the general pattern than individual numbers, since
 simultaneous appearance of two labels in one category is rare,
 but repeated rare events in a category is often not coincidence.
 For easy comparison, the color scale remains the same for
 (b) TikTok
 Fig. 7. Cosine similarity for the sports category.
 every matrix, which is between 0.9 and 1.
 Figure 5 correlates animals in Douyin and TikTok. We
 can see Douyin shows more numbers smaller than 1. It
 means animals in Douyin are more likely to appear together,
 especially for sheep and bird. This grouping behavior happens
 with big farms, popular animal parks, or even village streets.
 Douyin users treat animals as similar subjects while TikTok
 users tend to grant them with individual characteristics.
 Food in Figure 6 reveals an opposite distribution. TikTok
 users prefer to combine different foods and take videos to
gether (e.g. Broccoli and Orange). However, Douyin users
 have different food combinations. A pattern mismatch between
 numbers smaller than 1 shows a difference in convention
 between Douyin and TikTok. Users in different countries have
 different tastes and food preferences.
 This pattern remains for sports in Figure 7. More small
 numbers in TikTok means frequent and mixture of sports
 activity in groups. For example, they can use baseball bat to
 catch frisbee or sports ball. This flexible combination shows
 TikTok users tend to enjoy a complex and sports-heavy life.
 3) Label-Person Distribution: In this section, we examine
 each label’s average person frequency (Left of Figure 8).
 Average person frequency for a label is the total person
 frequency in frames with the label, divided by number of
 frames with the label. Overall, we discover that Douyin videos
 dominate in person frequency, with more frames that have
 more than two people. Tableware alone expresses stronger
 dominance (e.g. cup and knife). Their collective use may
 indicate that family members or friends meet together to have
 meals or union activities.
 Some labels in other categories are also interesting, like
 backpack and handbag in accessory. They outnumber other
 labels in TikTok, but are infrequent in Douyin. The fact their
 frequency are higher than 1 may be due to street snapshots
 and outdoor group activities. Douyin users often capture larger
Indoor 
Electronic 
Furniture 
Food 
Tableware 
Sports 
Accessory 
Animal 
Outdoor 
Vehicle 
Average Person Frequency 
Daily Life 
Relaxation &  
Entertainment 
Sports 
Sum of Output Scores 
Fig. 8. Average Label-Person Distribution (Left) and Human Action Frequency by Category (Right).
 groups of people or squares with high density of people.
 There is no obvious dominance in food. Cake leads in
 Douyin while apple leads in TikTok, with donut showing little
 difference. There is a mix in culture based on the user’s taste
 and preference. Food origin and their local custom decide a
 specific food popularity.
 B. Human Action Analysis
 Since person is the most frequent label, we analyze in detail
 their activities in videos. We discover that users like dancing
 and driving car in the video for both platforms. However, some
 general frequent actions are different. For example, people in
 the video like to have barbeque and dining in Douyin but
 like to do spray painting and writing in TikTok. We also
 divide actions into categories and compared their frequency
 differences sorted by category and super-category on the right
 of Figure 8. In general, working and daily actions are the two
 most frequent categories. Among these categories, Douyin has
 a clear lead in food and household, while TikTok leads in
 working and daily actions. The former two usually happen
 in a family and the latter two are more of a self-reflection.
 This implies that users in Douyin value families while users
 in TikTok value an independent lifestyle.
 At a higher level of the taxonomy, TikTok users are more
 likely to shoot sports videos, but people in Douyin are more
 likely to show relaxation and entertainment activities. It indi
cates that TikTok users are more willing to share the moments
when they are doing sports as a self-expression, but Douyin
 users are more casual and like to share their leisure moments.
 VI. CONCLUSION AND DISCUSSION
 Previously, little research has been devoted to treat TikTok
 and Douyin as separate data source with unique video identity.
 In this study, we have compared the cultural difference through
 video content analysis of these two popular short video apps.
 As a social media platform that requires a relatively simple
 production technique, they accommodate users with various
 social classes and cultural backgrounds. Our study takes this
 advantage and comprehensively uncover user interests, with
 two important general findings concluded below.
 First, on average, Douyin users show a simpler and more
 static lifestyle than TikTok. Similarity matrix analysis suggests
 that the labels in TikTok for static and daily-use objects are
 more likely to appear together under the same category. TikTok
 users show a tendency to capture diverse items, while many
 videos in Douyin consist of a clear subject and content focus.
 Second, family events dominate in Douyin in comparison
 of the individual events in TikTok. We have shown in label
 statistical analysis that Douyin and TikTok have dominance in
 different categories. Douyin has more frequent items in acces
sory and kitchen, while TikTok shows more in the category
 of electronic and appliance. The former about necessity and
 utensil is about the basics of life and the latter goes beyond
 the basics of life. The label-person distribution also confirms
 this finding. Videos in Douyin have more indoor items related
 to family members while TikTok users take videos by going
 outdoors to interact with friends and strangers. The hypothesis
 is further verified via the analysis of human actions. Sports is
 a major component in TikTok relative to Douyin. Relaxing
 activities, in contrast, appears more in Douyin than TikTok.
 VII. LIMITATION AND SIGNIFICANCE
 Our study detects the frequency differences of common
 objects between TikTok and Douyin. Such differences are due
 to cultural and user preferences by regions. To serve such
 market preferences, media companies in the short video space
 may design various application features in parallel to satisfy
 different user needs and provide users with a better experience.
 This study has certain limitations. Faster R-CNN and I3D
 are two common algorithms employed by previous object
 detection studies. They have been extensively verified and are
 relatively accurate. In terms of video input, models trained
 on the MS-COCO and Kinetics dataset provide sufficient
 indication of common user activities and interacted objects.
 Therefore, further increasing the accuracy of the classification
 models and including more fine-grained labels can strengthen
 our cultural analysis. In addition, culture may evolve over time
 and a longitudinal analysis may provide further insight.
 A video consists of not only frames but also audio and
 sometimes transcripts. Video background music and audio can
 provide additional information for cultural understanding as
 they also reflects user preferences and lifestyles. Some videos
 also contain textual content, such as logos, brands, and chats,
 which can be analyzed through OCR ad natural language
 processing to provide additional information.
 ACKNOWLEDGMENT
 This research is supported in part by the New York State
 CoE Goergen Institute for Data Science.
 REFERENCES
 [1] Shaoqing Ren, Kaiming He, Ross Girshick, and Jian Sun, “Faster
RCNN: Towards real-time object detection with region proposal net
works,” In advances in neural information processing systems, 2015,
 vol. 39, pp. 1139-1149. https://doi.org/10.1109/TPAMI.2016.2577031
 [2] Joao Carria and Andrew Zisserman, “Quo vadis, action recognition?
 A new model and the kinetics dataset,” IEEE conference on computer
 vision and pattern recognition, 2017, pp. 4724-4733.
 [3] Songyang Zhang, Tolga Aktas, and Jiebo Luo, “Mi YouTube es su
 YouTube? Analyzing the cultures using YouTube thumbnails of popular
 videos,” 2020, arXiv:2002.00842.
 [4] Avijeet Jha, Ashish Jha, Kushwaha Amarjeet, and Deepak Aeloor,
 “Video analysis with image recognition in TensorFlow,” Intl. Journal of
 Computer Science and Mobile Computing, 2019, vol. 8, pp. 103-107.
 [5] Shabbir Syed-Abdul, Luis Fernandez-Luque, Wen-Shan Jian, Yu-Chuan
 Li, Steven Crain, Min-Huei Hsu, Yao-Chin Wang, Dorjsuren Khan
dregzen, Enkhzaya Chuluunbaatar, Nguyen A. Phung, and Der-Ming
 Liou, “Misleading health-related information promoted through video
based social media: anorexia on YouTube,” Journal of Medical Internet
 Research, 2013, vol. 15, e30, https://doi.org/10.2196/jmir.2237
 [6] Yong Xu, Jixiang Dong, Bob Zhang, and Daoyun Xu, “Background
 modeling methods in video analysis: a review and comparative evalua
tion,” CAAI Transactions on Intelligence Technology, 2016, vol.1, pp.
 43-60. https://doi.org/10.1016/j.trit.2016.03.005
 [7] Ya˘gmur Lena and Dindaro˘glu Furkan, “Lingual orthodontic treatment:
 a YouTube video analysis,” The Angle Orthodontist, 2018, vol. 88, pp.
 208-214. https://doi.org/10.2319/090717-602.1
 [8] Ning Xu, Liejie Yang, Yuchen Fan, Jianchao Yang, Dingcheng Yue,
 Yuchen Liang, Brian Price, Scott Cohen, and Thomas Huang, “YouTube
VOS: sequence-to-sequence video object segmentation,” Proceedings of
 the European Conference on Computer Vision, 2018, pp. 585-601.
 [9] Quanzeng You, Dar´ ıo Garc´ıa-Garc´ ıa, Manohar Paluri, Jiebo Luo, and
 Jungseock Joo, “Cultural diffusion and trends in Facebook photographs,”
 In Proceedings of the Eleventh International AAAI Conference on Web
 and Social Media, 2017.
 [10] Sung-Eun Kim, Kyung Y. Lee, Soo I. Shin, and Sung-Byung Yang,
 “Effects of tourism information quality in social media on destination
 image formation: the case of Sina Weibo,” Information and Management,
 2017, vol.54, pp. 687-702.
 [11] Giuseppe Amato, Paolo Bolettieri, Vin´ ıcius Monteiro, Christina I.
 Muntean, Raffaele Perego, and Chiara Renso, “Social media image
 recognition for food trend analysis,” In Proceedings of the 40th Inter
national ACM SIGIR Conference, 2017, pp. 1333-1336.
 [12] Weibo Liu, Zidong Wang, Xiaohui Liu, Nianyin Zeng, Yurong Liu, Fuad
 E. Alsaadi, “A survey of deep neural network architectures and their
 applications,” Neurocomputing, 2017, vol. 234, pp. 11-26.
 [13] Matthew Pittman and Brandon Reich, “Social media and loneliness: why
 an Instagram picture may be worth more than a thousand Twitter words,”
 Computers in Human Behavior, 2019, vol. 62, pp. 155-167.
 [14] Jennifer Lai, Shailia Pervin, Anna Phan, and Wanita Sherchan, “Auto
matic detection of user personality traits based on social media image
 posts,” Patent NO. US 2017/0193533 A1, Filed Dec. 31st., 2015.
 [15] Qiyang Zhou, “Understanding user behaviors of creative practice on
 short video sharing platforms- a case study of TikTok and Bilibili,”
 University of Cincinnati, 2019.
 [16] Bahiyah Omar and Dequan Wang, “Watch, share or create: the influence
 of personality traits and user motivation on TikTok mobile video usage,”
 Intl. Journal of Interactive Mobile Technology, 2020, vol. 14, pp. 121.
 [17] Tsung-Yi Lin, Michael Maire, Serge Belongie, James Hays, Pietro Per
ona, Deva Ramanan, C. Lawrence Zitnick, and Piotr Doll´ar, “Microsoft
 COCO: common objects in context,” European Conference on Computer
 Vision, 2014, pp. 740-755.
 [18] Will Kay, Joao Carreira, Karen Simonyan, Brian Zhang, Chole Hillier,
 Sudheendra Vijayanarasimhan, Fabio Viola, Tim Green, Trevor Back,
 and Paul Natsev, “The kinetics human action video dataset,” 2017,
 arXiv:1705.06950. Retrieved from https://arxiv.org/abs/1705.06950.