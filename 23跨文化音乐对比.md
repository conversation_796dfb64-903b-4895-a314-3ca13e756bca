Received:28September2021-Revised:22November2021-Accepted:31December2021-CognitiveComputationandSystems
 DOI:10.1049/ccs2.12043
 ORIGINALRESEARCH
 Adataset for learningstylisticandculturalcorrelationsbetween
 musicandvideos
 XinyiChen1 | HuiZhang1 | SongruoyaoWu1 | JunZheng2 | LingyunSun1,3 |
 Ke<PERSON><PERSON>hang1,3
 1CollegeofComputerScienceandTechnology,
 ZhejiangUniversity,Hangzhou,China
 2The19thAsianGamesHangzhou2022Organising
 Committee,Hangzhou,China
 3Alibaba‐ZhejiangUniversityJointResearchInstitute
 ofFrontierTechnologies,Hangzhou,China
 Correspondence
 JunZheng,The19thAsianGamesHangzhou2022
 OrganisingCommittee,Hangzhou,China.
 Email: <EMAIL>
 Fundinginformation
 KeyR&DProgramofZhejiang,Grant/Award
 Number:2022C03126;TheKeyProjectofNatural
 ScienceFoundationofZhejiangProvince,Grant/
 AwardNumber:LZ19F020002;TheNational
 ScienceandTechnologyInnovation2030Major
 ProjectoftheMinistryofScienceandTechnologyof
 China,Grant/AwardNumber:2018AAA0100703
 Abstract
 Music–visual retrieval isofbroad interest in thefieldofMusic InformationRetrieval
 (MIR).Most research relies onemotional tags or is basedoncontent but does not
 considerstylisticandculturaldifferencesbetweenmusicandvideos.Asaresult,onlyone
sideddimensions are considered for automaticmusic retrieval for videos, while the
 stylistic correlationbetweenaudio‐visual is ignored. At the same time, the needs of
 differentculturalregionscannotbewellmet.Therefore,thefirstlabelledextensiveMusic
 Video(MV)dataset,Next‐MV, isconstructedinthispaperconsistingof6000piecesof
 30‐sMVfragments, includingfivemusicstyle labelsandfourcultural labels.Thepro
posedNext‐Net framework isbuilt tostudy the correlationbetweenmusic style and
 visual style.Theoptimalaudiovisual featuresetandmodel structureareobtainedinthe
 experiments. The accuracy reached 71.1%, higher than the baselinemodel (66.9%).
 Furthermore, inthecross‐culturalexperiment, itisfoundthattheaccuracyofthegeneral
 fusionmodel (71.1%) isbetween themodel trainedbywithin‐dataset (76%) and the
 modeltrainedbycross‐dataset(60%), indicatingthatculturehasasignificantinfluenceon
 the correlationbetweenmusic and visual. The experiments of pair classificationon
 cultures are further carriedout. It is found thatRockandDance aremoreculturally
 influenced thanR&BandHip‐hop. Among all the cultures discussed, Chinese and
 Japanesemusic and videos showgreat differences amongmost of the styles, while
 Koreanmusicvideosstylesaremoresimilartowesternstylesthanothereasterncultures.
 KEYWORDS
 crossculture, cross‐modalprocessing,deeplearningarchitectures,multimodal fusion,newdatasets
 1 | INTRODUCTION
 With the arrival of the 5Gera, short video has gradually
 replacedthe traditional formof text andimageswithits rich
 forms, fragmentationandinterestingness,andhasbecomethe
 most dominant andhottestmedia at present. Especially in
 China,well‐knownshortvideosharingplatformssuchasTik
TokandKwaihavegainedafootholdintheInternetmarket
 rapidly. The emergenceofUserGeneratedContent (UGC),
 ProfessionallyGeneratedContent (PGC), andOccupationally
 GeneratedContent (OGC) bringsmore traffic for the e
commerceindustry, foodindustryandothermajor industries.
 Therefore, how to automatically generate short videos
 quicklyandinbatcheshasbecomeoneofthehottestresearch
 topics.Oneofthewaysistoautomaticallyrecommendsuitable
 musicorsongsforshortvideos.Asisknown,Musicistheart
 ofarrangingsoundsintimetoproduceacompositionthrough
 theelementsofmelody,harmony,rhythm,andtimbre.Itisone
 of the universal cultural aspects of all human societies.
 Therefore,themusicofshortvideosplaysanimportantrolein
 the content expression and atmosphere rendering of short
 videos. For example, the same videowith different music
 emotionswillbringacompletelydifferenteffectofexpression.
 Hence,unlikethenormaltaskofcross‐modalretrievalbetween
 ThisisanopenaccessarticleunderthetermsoftheCreativeCommonsAttributionLicense,whichpermitsuse,distributionandreproductioninanymedium,providedtheoriginalworkis
 properlycited.
 ©2022TheAuthors.CognitiveComputationandSystemspublishedbyJohnWiley&SonsLtdonbehalfofTheInstitutionofEngineeringandTechnologyandShenzhenUniversity.
 Cogn.Comput.Syst.2022;4:177–187. wileyonlinelibrary.com/journal/ccs2-177
 25177567, 2022, 2, Downloaded from https://ietresearch.onlinelibrary.wiley.com/doi/10.1049/ccs2.12043 by National Institutes Of Health Malaysia, Wiley Online Library on [12/08/2025]. See the Terms and Conditions (https://onlinelibrary.wiley.com/terms-and-conditions) on Wiley Online Library for rules of use; OA articles are governed by the applicable Creative Commons License
 178
 CHEN
 ET AL
 .
 audio and video, how to match the right piece of music to the
 video can start with some unique properties of music, such as
 emotions, styles, rhythm, eras or cultures etc.
 Music style is a crucial dimension used to classify music.
 General definitions of music include common elements such
 as pitch (which governs melody and harmony), rhythm (and
 its associated concepts tempo, metre, and articulation), dy
namics (loudness and softness), and the sonic qualities of
 timbre and texture (which are sometimes termed the ‘colour’
 of a musical sound). Different styles of music may
 emphasise, de‐emphasise or omit some of these elements.
 Schindler et.al [1] tried to add visual features while
 extracting audio features for music genre/style classification,
 and the classification accuracy was indeed improved. It
 shows that visual elements have an impact on the expression
 of music style. This is also a useful conclusion for matching
 music to short videos.
 For example, if you need to match music to a very dramatic
 video, rock music is usually appropriate, while jazz and R&B
 music are not harmonious enough. Therefore, how to learn the
 visual differences between different music styles are the key to
 the problem, and the most significant step to solve this
 problem is to find appropriate data. At present, there are many
 videos with music, but most of them are produced by users
 themselves. Some of them are dynamic text to show lyrics, and
 some just use popular music without considering about con
tent matching. In this case, the fitness and harmony degree of
 music cannot be measured, and the quality is uneven. A Music
 Video (MV) is a kind of good training data. Because most of
 the official MVs are carefully shot by the director. The lighting,
 the performer's shape and the moving shot can well reflect the
 subjective imagery that the music hopes to express. Unfortu
nately, although MV data is not difficult to obtain, very few
 MV datasets can be labelled with music styles simultaneously.
 The Next‐MV constructed in this paper collected MV frag
ments containing labels of five different music styles, with an
 overall volume of 6000, which can well support the research on
 the correlations between music and video in style in this paper.
 In addition, it is worth noting that Chinese short video
 sharing platforms such as Tik Tok have launched overseas
 versions in an attempt to serve users from a wide range of
 cultures around the world. Nevertheless, there are many
 differences between those music‐related media apps designed
 for users with different cultural backgrounds, because people
 from different cultures have different understandings and
 preferences for music. The world contains a vast variety of
 types of music. This music arose as the result of complex
 geographical, historical, and prehistorical processes. The
 problem of determining the geographical origin of a piece of
 music is complicated. Over time, music from different re
gions have influenced each other, and many forms of music
 have travelled far from their points of origin [2]. It means
 music under different cultural backgrounds has commonal
ities and significant differences (local characteristics) [2].
 However, cultural factors have not been considered in
 research on the correlations between music and vision.
 Therefore, the Next‐MV dataset proposed in this paper not
 only contains style labels but also takes cultural dimension
 into consideration. MVs from China, Japan, South Korea,
 and America are collected to support cross‐cultural experi
ments. It is found that the accuracy of the general model
 trained with cross‐culture data is lower than that of the
 model trained with the data within the data set but better
 than that of the model trained with data from a single culture
 while testing data from a different culture. It shows that there
 are indeed cultural differences in the correlations between
 music and video. Thus, further pair classification experiments
 are conducted to further reach a cultural‐related conclusion.
 2
 2.1
 |
 RELATED WORK
 |
 Datasets and research studies related to
 music‐visual retrieval
 The most relevant dataset for the work in this paper is the
 MVD dataset constructed by Schindler [1] et al in 2015,
 containing three subsets: MVD‐VIS, MVD‐MM, and MVD
Mix. MVD‐VIS is used for music genre/style classification
 with visual features, which contains 8 music styles. Non
overlapping sub‐styles were chosen and tracks within a
 certain class share very similar musical characteristics. MVD
MM is used for multi‐modal music genre/style classification,
 containing another 8 music styles. The overlapping classes
 are chosen. MVD‐Mix combines MVD‐VIS and MVD‐MM,
 containing 16 music styles (Bollywood, Dance, Country,
 Latin, Metal, Opera, Rap, Reggae, the 80s, Dubstep, Folk,
 Hard Rock, Indie, Pop, Rock, Reggaeton, and RnB). This
 dataset aims to verify that the accuracy of music genre/style
 classification can be improved by adding visual features. The
 experimental results show that the accuracy of using visual
 features only is 50.13% in MVD‐VIS and 31.69% in MVD
MM. And the accuracy of classification is improved to
 different degrees when visual features are added to different
 music feature sets. These prove that visual features can
 reflect certain differences in those distinct music styles and
 the visual features matched by different music styles are
 distinguishable. However, due to the early construction of
 the MVD dataset, only audio feature and video feature sets
 can be downloaded on the official website at present. The
 original data (MV) cannot be downloaded anymore. In
 addition, the data collected by MVD are all western music,
 which is unable to use in the cross‐cultural experiments in
 this paper. Other datasets similar to this paper are all listed
 in Table 1.
 In 2018, Hong S et al. [3] studied content‐based video
music retrieval using deep neural networks. The low‐level
 audio features (such as melfrequncy cepstral coefficient
 (MFCC), spectral features, and so on) and video features (using
 two‐dimensional convolution to extract frame‐level features
 and then calculating the mean and variance of all the frames)
 are extracted straightly and fed into a two‐stream neural
 25177567, 2022, 2, Downloaded from https://ietresearch.onlinelibrary.wiley.com/doi/10.1049/ccs2.12043 by National Institutes Of Health Malaysia, Wiley Online Library on [12/08/2025]. See the Terms and Conditions (https://onlinelibrary.wiley.com/terms-and-conditions) on Wiley Online Library for rules of use; OA articles are governed by the applicable Creative Commons License
 CHEN
 ET AL
 .
 179
 network, which is called VM‐NET. The training data comes
 from the MV subset from YouTube‐8M, which contains 200k
 music videos without any labels. The method used in this paper
 is similar to our work, but it does not compare the different
 feature extraction methods of vision and music. Instead, a very
 traditional feature set is used. High‐level features extracted by
 the deep learning networks in this paper are proved to be more
 suitable descriptors for audiovisual modes. In this paper, more
 candidate values of full connecting (FC) layers are discussed.
 The YouTube‐8M dataset is mainly used in the field of video
 understanding. So, in addition to the official MV, there are
 many user‐made videos included in the MV subset, where vi
suals and music do not match well. So the quality is mixed. The
 absence of any label associated with music, such as styles or
 emotion, makes it difficult for researchers to explore more
 dimensions.
 In 2019, Gaurav V et al. [4] studied the affective cor
respondence between music and image, which is also similar
 to the work in this paper. For this task, a music clip and an
 image are considered similar (having true correspondence) if
 they have similar emotional content. The IMAC dataset is
 constructed, containing 3500 music clips and 85, 000 images
 with three emotion classes (positive, neutral and negative).
 Music and image features are extracted and fed into a two
stream neural network based on FC layers. The fusion layers
 combine two features for producing a binary label to reflect
 the affective correspondence between the two modalities.
 The proposed ACP‐Net is also applied to the task in this
 paper and compared with our method as a baseline.
 2.2
 |
 Cross‐cultural study related to music
 There are numerous cross‐cultural research studies on music.
 In 2014, X. Hu et al [8] built a collection of 1892 K‐pop
 (Korean pop) songs with mood annotations collected from
 both Korean and American listeners and analysed the differ
ences and similarities between the mood judgements of the
 two listening group. It is found that Americans assigned a
 larger number of labels to each song and applied more extreme
 valence and arousal values than Koreans. It is also found that
 compared to Americans, Koreans were more likely to label
 songs with negative moods such as ‘bittersweet’, ‘sad,’ and
 ‘mournful’. These observations were consistent with and
 supported by findings in previous cultural studies that people
 from western cultures tend to experience and/or express more
 positive emotions than those from eastern cultures [8]. The
 fact that Americans in this study could not understand the
 lyrics of the songs may have contributed to these results. It can
 be seen that people with different cultural backgrounds
 perceive different subjective emotions from music, which are
 affected by languages and customs.
 In 2017, Xiao H et al [9] studied the generalisability of
 mood regression models cross datasets. Three distinct datasets
 were used, which are MER60 (60 clips of western music),
 CH818 (818 clips of Chinese music), and AMG1608 (1608
 clips of western music). A series of experiments were con
ducted to examine different factors of the datasets, and their
 effects on music mood regression. It shows that within‐dataset
 predictions outperformed cross‐dataset predictions, and a
 larger size of training datasets helped regression performances
 on both valence and arousal dimensions. It shows that music in
 different cultural backgrounds has different ways of expressing
 emotions. Therefore, it is necessary to construct a specific
 mood classification model for music with different cultural
 backgrounds.
 According to Ref. [10–14], these studies mainly solve the
 problem that music (emotion) recognition models in different
 cultural backgrounds are not general. However, there has not
 been any cross‐cultural research on the connection between
 musicandvision.Forexample,whenitcomestoretrievingmusic
 for a video (vlogs or commercial advertising) automatically, the
 music should be selected differently for different cultures.
 3
 |
 DATASET CREATION
 To support the exploration of the correlation between music
 and videos in stylistic and cultural dimensions, the first cross
culture MV dataset Next‐MV is constructed in this paper
 containing a total of 6000 clips of MV clips. It is divided into
 four different cultures and five different styles, containing 300
 clips for each subset. The details of the dataset are shown in
 Figure 1. The representative keyframes for each subset are
 chosen randomly in Figure 1 to explicitly show the visual
 difference and are desensitised with a Gaussian blur.
 3.1
 |
 Label selection and data collection
 Music can be described in terms of many styles. Classifications
 are often arbitrary and may be disputed and closely related
 forms often overlap. Larger styles and styles comprise more
 specific sub‐categories. Music styles are divided into 20 cate
gories on Wikipedia [20], while music styles in the existing
 music genre/style datasets are all different. There are 10 styles
 in GTZAN [15], six in ISMIR [16], and nine in Homburg [17].
 See Table 2 for detailed classification.
 Music style labels are relatively easy to collect while difficult
 when it comes to MVs due to not all music or songs are filmed
 in the video. Therefore, some specific music styles (such as
 classical, electronic, and so on) lack official MV resources. Also,
 some styles of music such as country or Latin are more popular
 in western countries than in China. So those music videos in
 eastern are rare.
 Consider balancing the amount of MV data under all style
 tags in the dataset; five music styles with rich MV resources in
 both East and West are selected. They are Pop, R&B, Hip‐hop,
 Dance, and Rock. To support cross‐cultural related research,
 MVs from both East and West are collected in this dataset.
 The definition of culture is broad, and there is currently
 no study that has a standard division of cultures in the
world. Most previous music‐related cross‐cultural studies
 contrasted the differences betweenChinese [8, 12, 14] and
 western cultures orKorean andwestern [9, 13]. It canbe
 seen thatmore researchers areconcernedabout thecultural
 differences between the East and theWest. Due to the
 diversityofAsiancultures,Chinese,KoreanandJapanese, as
 representative easterncultures, are selected into the cultural
 labels of the dataset. There are four cultural labels in this
 dataset: Chinese, Japanese,Korean, andAmerican.
 Representativesingers listsofdifferentstylesarecollected
 fromauthoritativemusicplatformsandtheirofficialMVsare
 downloaded inbatchfromYouTubeandBilibili if available.
 FIGURE1 Detailsof theNext‐MVdataset.Next‐MVconsists in6000MusicVideo(MV)clips intotal, containingfourdifferentcultures (American,
 Chinese,KoreanandJapanese)andfivestyles(Rock,Hiphop,R&B,DanceandPop).Thesubset ineachstyleandeachculturecontains300MVclips
 TABLE1 Theexistingmusic‐related
 mutimediadataset Year Dataset Size Typesof labels
 2007 Videos‐100[5] 100 MVtypes
 2014 Deap[6] 3580 electroencephalogram(EEG)
 2015 MVD[1] 1600 Styleandartists
 2018 MV‐10k[7] 10000 None
 2018 HIMV‐200k[3] 200k None
 2019 IMAC[4] 3500musicand85kimages Emotions
 2021 Next‐MV 6000 Style, cultures, andartists
 TABLE2 TheexistingmusicGenre/Styledataset
 Year Dataset Size Class Genre/style
 2002 GTZAN[15] 1000 10 Classical, country,disco,hiphop, jazz, rock,blues, reggae,pop, andmetal
 2004 ISMIR[16] 1458 6 Classical, electronic, jazz‐blue,metal‐punk, rock‐pop, andworld
 2005 Homburg[17] 1886 9 Pop, rock, folk/Country, alternative, jazz, electronic,blues…
 2004 MSD[18] 1000k 21 Pop/Rock, electronic, rap, jazz,Latin,R&B, International, country, religious…
 2004 FMA[19] 106574 163 Rock, electronic,hip‐hop, folk,pop, jazz…
 180-CHENETAL.
 25177567, 2022, 2, Downloaded from https://ietresearch.onlinelibrary.wiley.com/doi/10.1049/ccs2.12043 by National Institutes Of Health Malaysia, Wiley Online Library on [12/08/2025]. See the Terms and Conditions (https://onlinelibrary.wiley.com/terms-and-conditions) on Wiley Online Library for rules of use; OA articles are governed by the applicable Creative Commons License
4.1
 |
 |
 Music‐visual representation
 25177567, 2022, 2, Downloaded from https://ietresearch.onlinelibrary.wiley.com/doi/10.1049/ccs2.12043 by National Institutes Of Health Malaysia, Wiley Online Library on [12/08/2025]. See the Terms and Conditions (https://onlinelibrary.wiley.com/terms-and-conditions) on Wiley Online Library for rules of use; OA articles are governed by the applicable Creative Commons License
 CHEN
 ET AL
 .
 181
 Insufficient representative singers are found in subsets, so a
 portion of MVs are gathered by querying YouTube/Bilibili
 using the tags ‘official MV playlist’ in different languages such
 as American, Japanese, Korean and Chinese. In order to
 ensure the collected dataset could perform only difference in
 style and culture, we collect as many singer lists as possible
 under each music style, and at the same time controlled their
 gender ratio at 1:1. It is also ensured that the singers and
 their songs we collected are all modern singers in the 21st
 century.
 3.2
 |
 Data cleaning
 Not all the songs from those representative singers belong to
 the same style, so it is necessary to carry on the cleaning work
 to the originally collected data. The two‐step work is per
formed in the following order:
 1. Auto‐tagging screening out: A music style classifier based
 on the Vggish [21] model is pre‐trained on the Homburg
 [17] dataset and achieved 94% accuracy on the testing set. It
 is used to auto‐tag all the raw MV data. When the predicted
 style label does not match the original label, this item of MV
 data is screened out. Since the machine marking tool has a
 certain error rate, this may lead to the loss of some data
 originally labelled correctly, but no disputing data is gained
 and the reliability of the retained data labels is guaranteed.
 2. Human screening out: Each MV data has been manually
 reviewed to ensure that the video is official (some videos
 use dynamic text to show lyrics or use still pictures, which
 are considered irrelevant between music and video).
 3.3
 |
 Data processing
 The length of different MVs varies from 3 to 7 min. To enrich
 the data quantity, each MV is divided into several 30‐s clips. At
 the beginning and end of some MVs, non‐music fragments
 such as ambient sound and voice‐over are regarded as distur
bance terms in music‐related research. These kinds of clips are
 screened out. Due to copyright restrictions, it is not possible to
 redistribute music videos or audio files. Yet, all videos have
 been retrieved from the Youtube and Bilibili platform. The list
 of corresponding URLs can be provided.
 4
 |
 STYLE CORRESPONDENCE
 LEARNING
 Learning the relationship between music style and visual style
 can help retrieve more suitable music for videos. A framework
 named Next‐Net is presented for style correspondence
 learning between music and video. The system overview of the
 proposed Next‐Net is shown in Figure 2. The optimal feature
 sets and model architectures are further chosen.
 4.1.1
 Music features extraction
 Traditional acoustic features are widely used for music emotion
 or genre/style recognition [22–24].
 This feature set consists mainly of MFCCs, chroma fea
tures, spectral contrast features [25], tonal centroid features
 [26], and additional features from the mel spectrogram. Table 3
 summarises these features used as the traditional low‐level
 music feature extractor and compared them with our new
 extractor. For each music clips A
 n
 tures extracted by it.
 , L
 n
 is denoted as the fea
SoundNet [27] is a model which is used to learn rich
 natural sound representations by capitalising on large
 amounts of unlabelled sound data collected in the wild and
 yields significant performance improvements over the state
of‐the‐art results on standard benchmarks for acoustic
 scene/object classification. It is the first time that is chosen
 to be used as the high‐level music feature extractors in this
 paper. All the music clips A
 n
 are fed into the SoundNet
 extractor pre‐trained on over 2,000,000 unlabelled videos,
 through six one‐dimensional convolutional neural networks
 with different kernel sizes shown in Figure 2. Since the last
 layer of SoundNet is used as the output of sound classifi
cation in which semantics level is too high, it is abandoned by
 this paper and the 1024‐dimensional vector of the penulti
mate layer of SoundNet is extracted. The 512‐dimensional
 vector of the third layer from the bottom is also extracted
 for effect comparisons. For each A
 n
 , latent space vectors are
 obtained by the SoundNet extractor, whose shape is
 (21,1024) and (21,512). A mean value is calculated so that the
 f
 inal music vector is S
 1024n
 1024 and 512, respectively.
 4.1.2
 |
 and S
 512n
 , whose dimensions are
 Video features extraction
 Traditional image features are widely used for music–visual
 retrieval [3, 4, 29]. These feature sets use traditional image
 features such as Red, Green, Blue (RGB) or two‐dimen
sional convolutional networks pre‐trained in ImageNet to
 gain frame‐level features and mean, the variance value is
 calculated to represent video‐level features. The Y
 n
 ¼YðnÞ
 is used as the traditional RGB features to compare with new
 extractors. In this paper, video‐level features are first to be
 extracted by the three‐dimensional convolutional network
 [30] to realise the music–visual retrieval task, which is
 proved to work better on the video classification task. Ac
cording to the keyframes sequences V
 k
 , 16 is chosen as the
 k’s value. These frames are fed into the C3D extractor
 through five three dimensional convolutional networks and a
 2048‐dimensional FC layer. The architecture of the C3D
 extractor is shown in Figure 2. The output 2048‐dimension
 vectors are the gained video features, denoted as C
 2048n
 .
 The C3d extractor is pre‐trained on the Sport1M [31]
dataset.Theeffectsof thefeaturesgainedwith Cp2048n
 �and
 without C2048n ð Þ pre‐trained C3d extractor are also
 compared.
 4.2 | Music‐visual fusion
 To integrate themusic and video features, they shouldbe
 mapped into the same semantic space.Atwo‐branchneural
 networkisconstructed.
 LetC¼Rock;Hiphop;R&B;Dance;Pop ð Þbe the set of
 all thestylelabels,andV;Mdenotethesetsofvideosamples
 andmusicsamples.Thetaskof learningcorrelationsbetween
 musicstyleandvisualstyleisnotconsideredasaclassification
 probleminthispaperbecausethebinaryclassificationof ‘yes’
 or ‘no’ issimpleandhasbeenusedinRef. [4].Therefore,we
 proposeanovel approachof triplet‐fusionnetworksothat a
 similaritymetricislearntfrommusicandvideoswiththesame
 style. The effect of themodel is also comparedwith the
 classificationmethod[4].
 Given a training set T¼Vi;Mi;Ci ð Þn
 i¼1, where n is
 denotedas the total sizeof dataset,Vi andMi aredenoted
 as the featuresofmusicandvideoandCi is the style label
 of thecorrespondingmusicandvideo.Different setscanbe
 chosenusingdifferent featureextractorsmentionedabove in
 4.2. Our goal is to learn a similaritymetric SwithT to
 predict the similarity degree, given a new music pair
 vj;mk
 � . It contains two steps in audiovisual fusionprog
ress. First, the triplet set T0 are allocated from T.
 T0¼Vi;Pi;Nj
 �n
 i¼1;j¼1
 ,whereVi is thevideofeature,which
 is seenas ananchor, Pi is themusic feature,whichhas the
 samestyle labelwithVi, andNj is themusic feature,which
 has adifferent style labelwithVi. The samepairVi;Pi is
 matchedmore than one Nj, in which j is satisfied that
 Ci≠Cj.Triples that far exceed the total sizeof thedataset
 areobtained,whichexpands the trainingset.Then,T0 is fed
 into the proposed fusion network containing two sub
networks toprocess twomodalities.
 Foreachtriplet Vi;Pi;Nj
 � , twosubnetof fourFClayers
 with different nodes size are constructed for yielding two
 modilities, respectively, intoafinal16‐dimensionalembedding,
 which is denoted as FðiÞ¼v16i;p16i;n16i ð Þ. The similarity
 fuctionsbetweenmusicmandvideovisdefinedas [32]:
 Dvi;mi ð Þ¼vT
 imi
 �kvik�kmik ð Þ
 Andthetriplet loss isdefinedas [33]:
 Loss¼
 Xn
 i¼0
 Dv16i;p16i�−Dv16i;n16i�þMargin�
 FIGURE2 Frameworksof theproposedmethod:Next‐Net
 TABLE3 Thetraditional low‐levelmusicfeaturesextractedby
 Librosa[28]
 Types Features
 MFCCs MFCC,mel spectrogram, anddelta‐MFCC(1stand2nd)
 Spectral Spectral centroid, spectralbandwidth, spectral rolloff, andpoly
feature(1stand2nd)
 Chroma Chroma‐censandchroma‐STFT
 Etc. Zero‐crossingandroot‐mean‐squareenergy
 TABLE4 Detailsofaccuracyachievedondifferent featuresets
 Featuresets Yn C2048n Cp2048n
 Ln
 0.643 0.558 0.698
 S512n
 0.685 0.553 0.692
 S1024n
 0.67 0.562 0.711
 182-CHENETAL.
 25177567, 2022, 2, Downloaded from https://ietresearch.onlinelibrary.wiley.com/doi/10.1049/ccs2.12043 by National Institutes Of Health Malaysia, Wiley Online Library on [12/08/2025]. See the Terms and Conditions (https://onlinelibrary.wiley.com/terms-and-conditions) on Wiley Online Library for rules of use; OA articles are governed by the applicable Creative Commons License
whereMarginisthehyper‐parameter.Itrecordsthedifference
 between thedistancebetween thepositive example and the
 anchorandthedistancebetweenthenegativeexampleandthe
 anchor.When thepositive example is closer to the anchor,
 thesmaller the lossvalue, andviceversa, thegreater the loss
 value.TheADAMoptimiser is adopted, setting the learning
 rateas0.001.Aftertraining,whengivenavideoVandalistof
 musicMk, thefusionmodelusesthelearningtransformationS
 toturnVtov16 andturnMk tom16
 k ; thesimilarityfunctions
 are calculatedon v16;m16
 k
 � tofind the suitablemusic lists
 whicharethesamestyleas thevideo.
 4.3 | Experimentandresult
 All the data inNext‐MV is used in the audiovisual fusion
 experiment. Totally6000music andvideoclips are fed into
 Next‐Netanddividedinto677,471trainingtriplesand170,556
 testingtriples.Whenthelossvaluenolongerdrops,thesystem
 automatically stops training. For the evaluationmetrics,Ac
curacyandRecall@Kareapplied toour task. Similar tothe
 classificationtask, theAccuracyfunctionisdefinedas [33]:
 Acc¼Nc
 �n¼1
 n
 Xn
 i¼1
 relaðiÞ
 wherenisthetotalnumberoftriplesetsandrelaðiÞisabinary
 value which is 1, if Dv16i;p16i ð Þ<Dv16i;n16i ð Þ, and is
 0otherwise. It iseasy tounderstandthat if thedistancebe
tweenanchorandpositivesamplesissmallerthanthatbetween
 anchor andnegative samples, it is consideredcorrect in this
 turnofpredicting.Recall@KmeansthefirstKrecommended
 songsaretherightstylematchingtothevideo.It isdefinedas:
 Recall@K¼Nf
 �nv¼ 1
 nv
 Xnv
 i¼1
 relbðiÞ
 wherenv is the total sizeofVi andrelbðiÞ isabinaryvalue
 whichis1, if theelement intheset
 Dv16i;p16i��<Dv16i;n16i� � �K
 i¼0
 is all True and is 0otherwise. Itmeans, inamusic recom
mendation task, theprobability that the stylesof the topK
 musicmatchtheoriginalvideo.
 1. Optimal featuresetschoosing:Different featuresetson
 bothmusicandvideoarefirstcomparedinTable4. Itcan
 beseenthat featuresextractedbythedeeplearningmodel
 (higher than70%)performbetter thanthetraditional low
level features (lower than65%) in the audiovisual fusion
 task.Also, theperformanceof thedeep learning feature
 extractorpre‐trainedinlargedatasets (higher than70%) is
 far better than that of the untrained feature extractor
 (lower than 60%). Consequently, the S1024n and the
 Cp2048n are chosen as the optimal feature sets for our
 Next‐Net.
 2. Optimal model structures choosing: The fusion
 network inNext‐Net is constructedbyFClayers, a total
 of 10network structureswith the different numbers of
 layers or nodes and the baselinemodel (ACP‐NET [4],
 which isused in learningmoodcorrespondencebetween
 music and images.) have been tested. See Table 5 for
 details.Theresult shows thatNext‐Netwithmusic‐bunch
 nodes of (512, 256, 64, and16) andvideo‐bunchnodes
 (1024,512,128,and16),whoseaccuracyis0.711, isbetter
 performed thanothermodels. It can also be seen that
 four‐layermodelsperformbetter thanthree‐layermodels.
 Some network structures with different node combina
tionsgainsimilar accuracy.
 5 | CROSS‐CULTURALEXPERIMENTS
 Toour best knowledge, nostudieshave consideredcultural
 differences in retrievingmusic for videodue to the lackof
 culture tags on the existingMVdatasets.However, cultural
 TABLE5 Accuracyachievedon
 differentmodels Name Music‐bunchnodes Video‐bunchnodes Accuracy Recall@1
 Next‐Net‐V1 512,256,64,16 1024,512,128,16 0.711 0.801
 Next‐Net‐V2 512,128,64,16 1024,256,128,16 0.701 0.813
 Next‐Net‐V3 512,256,128,16 1024,256,64,16 0.683 0.712
 Next‐Net‐V4 512,256,64,16 1024,512,128,16 0.692 0.787
 Next‐Net‐V5 256,64,16 1024,512,128,16 0.671 0.724
 Next‐Net‐V6 256,128,16 1024,512,128,16 0.689 0.703
 Next‐Net‐V7 512,256,64,16 512,128,16 0.678 0.718
 Next‐Net‐V8 512,256,64,16 512,256,16 0.683 0.721
 Next‐Net‐V9 512,256,64,16 256,128,16 0.621 0.698
 Next‐Net‐V10 512,256,64,16 256,64,16 0.617 0.707
 ACP‐Net [5] 512,2048,1024 2048,1024 0.661
CHENETAL.-183
 25177567, 2022, 2, Downloaded from https://ietresearch.onlinelibrary.wiley.com/doi/10.1049/ccs2.12043 by National Institutes Of Health Malaysia, Wiley Online Library on [12/08/2025]. See the Terms and Conditions (https://onlinelibrary.wiley.com/terms-and-conditions) on Wiley Online Library for rules of use; OA articles are governed by the applicable Creative Commons License
5.1
 |
 25177567, 2022, 2, Downloaded from https://ietresearch.onlinelibrary.wiley.com/doi/10.1049/ccs2.12043 by National Institutes Of Health Malaysia, Wiley Online Library on [12/08/2025]. See the Terms and Conditions (https://onlinelibrary.wiley.com/terms-and-conditions) on Wiley Online Library for rules of use; OA articles are governed by the applicable Creative Commons License
 184
 CHEN
 ET AL
 .
 differences do have an impact on people's subjective
 aesthetic appreciation. The following experiments verified
 that the effects of cultural differences on music–visual
 fusion do exist, and the specific differences are further
 found out.
 TABLE6 Style correspondence between different cultures
 Exps
 Training set + Testing set
 Accuracy
 Recall@1
 Exp0
 Exp1
 Exp2
 Exp3
 Exp4
 Exp5
 Exp6
 Exp7
 Exp8
 Exp9
 Exp10
 All (Train)+All (Test)
 Chinese (Train)+Chinese (Test)
 Korean (Train)+Korean (Test)
 Japanese (Train)+Japanese (Test)
 American (Train)+American (Test)
 Chinese (Train)+American (Test)
 Japanese (Train)+American (Test)
 Korean (Train)+American (Test)
 Chinese (Train)+Korean (Test)
 Chinese (Train)+Japanese (Test)
 Japanese (Train)+Korean (Test)
 0.711
 0.763
 0.745
 0.767
 0.765
 0.613
 0.557
 0.632
 0.590
 0.611
 0.554
 0.801
 0.809
 0.823
 0.790
 0.857
 0.723
 0.714
 0.687
 0.550
 0.662
 0.634
 Style correspondence between different
 cultures
 To explore whether cultures impact the style correspondence
 between music and video, a series of experiments are con
ducted on cross‐datasets. The performance of the model
 trained on the single cultural data was compared with that of
 the general model (trained on all data). The details are shown
 in Table 6. In experiments 1–4, we built models with the
 same size of training sets and testing sets by randomly
 selecting 1200 and 300 examples from the within‐subsets. In
 experiments 5–7, models are built with the same size of
 training sets from the eastern subsets and testing sets from
 the American subsets, respectively, aiming to study the dif
ference between the west and east. In experiments 8–10,
 with the aim of studying the difference in Asian, models are
 built with the training sets and testing sets both from the
 Asian subsets, with the same size as mentioned above. In
 experiment 0, models are trained by the training sets and
 testing sets from four cultures. The following observation
 can be made.
 1. The models trained and tested on within‐datasets
 (up to 0.76) show remarkably better results on those
 models trained and tested on cross‐datasets (no more than
 FIGURE3 Thearchitecture of Next‐Net‐Classification
0.64) and the generalmodel (0.66). This indicates that the
 influenceof cultural backgroundon themusicvideos does
 exist.
 2. Themodels trained on theKorean subsets (0.632)
 work much better on the American subsets while the
 models trained on the Japanese subsets (0.557) work the
 worst.This indicates that themusicstyleandvisual styles in
 Korean music videos are relatively similar to American
 musicvideos.
 3. Themodels trainedon the Japanese subsets (0.557,
 0.554, and0.565)worktheworstonanyother subsetswhile
 themodels trainedonChinese subsets (0.613, 0.590, 0.611,
 and0.603)works relativelybetter. It indicates that Japanese
 musicvideosshowthemostethniccharacteristicsandChinese
 musicvideosarethemostwidespread.
 5.2 | Pairclassificationsoncultures
 It is indeedfoundintheaboveexperiments that thecultural
 influence on themusic‐video fusiondoes exist. To further
 explore the differences betweenmusical culture and visual
 culture indifferent countries, pair classificationexperiments
 ondifferentculturesarecarriedout.
 Wemodified thenetworkstructureofNext‐Net slightly
 and obtained the Next‐Net‐Classification (NNC) model,
 whichisusedfor theclassificationtaskamongcultures.The
 architectureofNNCisshowninFigure3.
 First, through feature extractors, bothmusic andvideo
 features, S1024n andCp2048n, areobtained.After the last full
 connection layer of the original fusion network, a full
 connection layerwith2‐dimensional vector output is added
 toget abinary result inboth twobunchesof thenetwork.
 Inthisway, theMVofdifferent culturescanbeclassified in
 music and videos simultaneously. Due to the data volume
 being not large, SupportVectorMachine (SVM) is chosen
 as the baseline for comparing the classification accuracy.
 The details of the results are shown inTable 7. Inmost
 cases, the accuracyofNNCishigher than that of SVM. It
 canbe seen that:
 1.Towardsrock,ChineserockandAmericanrock(0.81on
 music, and0.83onvideo),Chinese rockand Japanese rock
 (0.79onmusic, and0.75onvideo)achievehighclassification
 accuracyinbothvisual andacoustical aspects.Americanrock
 and Japanese rockshowagreat differenceonmusic (0.79),
 whileChineserockandKoreanrockshowagreatdifference
 onvideo (0.84).Korean rockand JapaneseRock showfew
 differences invisual (only0.54).
 2.All theeasternculturesandwesternculturesshowgreat
 visualdifferences inDance(upto0.75).
 3. There are fewdifferences inHip‐hopmusic among
 mostcultures(lowerthan0.65),whileAmericanHip‐hopand
 JapaneseHip‐hop, Chinese and JapaneseHip‐hop showa
 greatdifference invideos (upto0.78).
 Consequently, severalobservationscanbemade.
 1. Rock andDance aremore culturally influenced than
 R&BandHip‐hop.
 TABLE7 Pairclassificationonstylesfromdifferentcultures
 Pop Rock Dance Hiphop R&B
 Music Video Music Video Music Video Music Video Music Video
 SVM NNC SVM NNC SVM NNC SVM NNC SVM NNC SVM NNC SVM NNC SVM NNC SVM NNC SVM NNC
 AmericanChinese 0.72 0.70 0.72 0.71 0.80 0.81 0.81 0.83 0.62 0.72 0.79 0.79 0.58 0.63 0.74 0.73 0.68 0.67 0.55 0.51
 AmericanJapanese 0.63 0.72 0.66 0.67 0.72 0.79 0.68 0.69 0.75 0.78 0.75 0.76 0.63 0.65 0.75 0.78 0.68 0.69 0.74 0.74
 AmericanKorean 0.77 0.68 0.65 0.72 0.66 0.64 0.71 0.74 0.78 0.87 0.84 0.86 0.73 0.73 0.58 0.61 0.59 0.63 0.55 0.62
 ChineseJapanese 0.69 0.77 0.61 0.74 0.77 0.79 0.68 0.75 0.73 0.75 0.73 0.73 0.57 0.56 0.71 0.83 0.71 0.74 0.53 0.61
 ChineseKorean 0.67 0.73 0.76 0.79 0.64 0.74 0.76 0.84 0.83 0.73 0.60 0.65 0.54 0.47 0.61 0.64 0.73 0.82 0.76 0.84
 JapaneseKorean 0.63 0.69 0.68 0.62 0.69 0.74 0.59 0.54 0.55 0.65 0.67 0.72 0.65 0.66 0.61 0.63 0.70 0.68 0.70 0.79
 CHENETAL.-185
 25177567, 2022, 2, Downloaded from https://ietresearch.onlinelibrary.wiley.com/doi/10.1049/ccs2.12043 by National Institutes Of Health Malaysia, Wiley Online Library on [12/08/2025]. See the Terms and Conditions (https://onlinelibrary.wiley.com/terms-and-conditions) on Wiley Online Library for rules of use; OA articles are governed by the applicable Creative Commons License
DATA AVAILABILITY STATEMENT
 25177567, 2022, 2, Downloaded from https://ietresearch.onlinelibrary.wiley.com/doi/10.1049/ccs2.12043 by National Institutes Of Health Malaysia, Wiley Online Library on [12/08/2025]. See the Terms and Conditions (https://onlinelibrary.wiley.com/terms-and-conditions) on Wiley Online Library for rules of use; OA articles are governed by the applicable Creative Commons License
 186
 CHEN
 ET AL
 .
 2. Chinese and Japanese music and videos show great
 differences between each other among most of the styles.
 3. The Korean MV is more similar to the western MV
 compared to other eastern countries.
 These observations are of great use, especially when music
 is needed to be automatically matched for videos from
 different cultural backgrounds, better matching can be ach
ieved through more targeted training models.
 6
 |
 CONCLUSION
 This article mainly has the following three innovations and
 contributions: 1. A new cross‐cultural MV dataset is intro
duced, namely, Next‐MV, which is composed of 6000 MV
 clips of five styles and from four cultures. It is built to study
 the correspondence between music and visuals. It is con
structed to make up for the absence of high‐quality audio
visual datasets with cultural and style tags. It can support
 most of the research studies related to integration of music
 and vision as well as the study of music style classification,
 video style classification research, and cross‐cultural corre
lation. 2. A cross model called Next‐Net is proposed to map
 music and video from individual modalities to a common
 vector space. The optimal feature sets and model structures
 are chosen by experiments and better prediction accuracy is
 gained. According to the experimental results, the correlation
 between music style and visual style does exist so that styles
 can also be an important dimension of music–visual
 retrieval, just like emotion. 3. It is the first time to study
 the influence of cultural factors on the video soundtrack
 task. We found that cultural backgrounds have an impact on
 the integration of music and vision. Different cultures have
 significant differences in music or videos of the same style.
 The findings could provide a better guide to retrieve suitable
 music for short videos from different cultures. In the future,
 human–computer interaction experiments related to culture
 background and video content will be carried out based on
 this dataset, to find a more concrete mapping relationship
 between them. A friendly interactive system will also be built
 to implement the algorithm into a useable short video
 soundtrack platform. In this soundtrack system, users can
 select models and music libraries with specific cultural tags,
 so as to provide users with different cultural backgrounds
 with more accurate and personalised video soundtrack
 services.
 ACKNOWLEDGEMENT
 This work was supported by the Key R&D Program of Zhe
jiang Province (No. 2022C03126) and the Key Project of
 Natural Science Foundation of Zhejiang Province (No.
 LZ19F020002).
 CONFLICT OF INTEREST
 None.
 The dataset constructed in this article will be made public on
 GitHub for other researchers to reproduce.
 ORCID
 Xinyi Chen https://orcid.org/0000-0001-6573-0811
 REFERENCES
 1. Schindler, A., Rauber, A.: An– approach to music genre classification
 through affective color features. In: European conference on informa
tion retrieval (2015)
 2. Fang, Z., Claire, Q., King, R.D.: Predicting the geographical origin of
 music. IEEE (2015)
 3. Hong, S., Im, W., Yang, H.S.: Cbvmr: Content‐based video‐music
 retrieval using soft intra‐modal structure constraint. In the 2018 ACM,
 (2018)
 4. Verma, G., Dhekane, E.G., Guha, T.: Learning affective correspondence
 between music and image. In: ICASSP 2019‐ 2019 IEEE International
 conference on acoustics, speech and signal processing. ICASSP (2019)
 5. Gillet, O., Essid, S., Richard, G.: On the correlation of automatic audio
 and visual segmentations of music videos. IEEE Trans. Circ. Syst. Video
 Technol. 17(3), 347–355 (2007)
 6. Koelstra, Deap, S.: A database for emotion analysis ;using physiological
 signals. IEEE transactions on affective computing (2012)
 7. Zeng, D., Yi, Y., Oyama, K.: Audio‐visual embedding for cross‐modal
 music video retrieval through supervised deep cca. In 2018 IEEE In
ternational symposium on multimedia. ISM), (2018)
 8. Lee, J., Hu, X.: Cross‐cultural similarities and differences in music mood
 perception. iSchools. (2014)
 9. Hu, X., Yang, Y.H.: Cross‐dataset and cross‐cultural music mood pre
diction: a case on western and Chinese pop songs. IEEE Trans Affect
 Comput. 8(2), 228–240 (2017)
 10. Silverman, M., et al.: Daily temporal self‐care responses to osteoarthritis
 symptoms by older african americans and whites. J. Cross Cult. Gerontol.
 23(4), 319–337 (2008)
 11. Fan, J., et al.: A comparative study of western and Chinese classical music
 based on soundscape models (2020)
 12. Hu, X., Yang, Y.: The mood of Chinese pop music: representation and
 recognition. J. Am. Soc. Inf. Sci. Technol. 68(8), 1899–1910 (2017)
 13. Hu, X., et al.: A cross‐cultural study on the mood of k‐pop songs
 14. Hu, X., Lee, J.H.: A cross‐cultural study of music mood perception be
tween american and Chinese listeners (2012)
 15. Sturm, B.L.: The gtzan dataset: its contents, its faults, their effects on
 evaluation, and its future use. Eprint Arxiv (2013)
 16. Cano, P., et al.: 2004 Audio description contest. Ismir, (2006)
 17. Homburg, H., Mierswa, I.: A benchmark dataset for audio classification
 and clustering. ISMIR. 2005, 528–31 (2005)
 18. Example, A., Description, T.: Million song dataset (2012)
 19. Defferrard, M, et al.: A dataset for music analysis (2016)
 20. Music, May 2021
 21. Chen, H., et al.: A large‐scale audio‐visual dataset. ICASSP 2020‐ 2020
 IEEE International Conference on acoustics, Speech and signal pro
cessing. ICASSP (2020)
 22. Meng,A.,etal.:Temporalfeatureintegrationfor musicgenreclassification.
 IEEE Trans. Audio Speech Lang. Process. 15(5), 1654–1664 (2007)
 23. Poria, S., et al.: Music genre classification: a semi‐supervised approach.
 Pattern Recognition (2013)
 24. Zhao, Y., et al.: A universal music‐acoustic encoder based on trans
formers (2020)
 25. Jiang, D.N., et al.: Music type classification by spectral contrast feature.
 In: Proceedings. IEEE International Conference on Multimedia and
 Expo (2002)
 26. Harte, C., Sandler, M., Gasser, M.: Detecting harmonic change in musical
 audio. In: Acm workshop on audio music computing multimedia, page 21
 (2006)
 25177567, 2022, 2, Downloaded from https://ietresearch.onlinelibrary.wiley.com/doi/10.1049/ccs2.12043 by National Institutes Of Health Malaysia, Wiley Online Library on [12/08/2025]. See the Terms and Conditions (https://onlinelibrary.wiley.com/terms-and-conditions) on Wiley Online Library for rules of use; OA articles are governed by the applicable Creative Commons License
 CHEN
 ET AL
 .
 187
 27. Aytar, Y, Vondrick, C, Torralba, A.: Soundnet: learning sound repre
sentations from unlabeled video. In: Advances in neural information
 processing systems (2016)
 28. McFee, B, et al.: Librosa: audio and music signal analysis in python.
 Proceedings of the 14th python in science conference, vol. 8, pp. 18–25.
 Citeseer (2015)
 29. Wu, X.: Cross Matching of Music and image. PhD thesis. The Chinese
 University of Hong Kong (Hong Kong) (2015)
 30. Tran, D., et al.: Learning spatiotemporal features with 3d convolutional
 networks (2014)
 31. Karpathy, A., et al.: Large‐scale video classification with convolutional
 neural networks. In: Computer vision pattern recognition (2014)
 32. Rasiwasia, N, et al.: Cluster canonical correlation analysis. In: Artificial
 intelligence and statistics, pp. 823–831. PMLR (2014)
 33. Schroff, F, Kalenichenko, D, James, P.: Facenet: a unified embedding for
 face recognition and clustering. CoRR, abs/. 1503, 03832 (2015)
 How to cite this article: Chen, X., et al.: A dataset for
 learning stylistic and cultural correlations between music
 and videos. Cogn. Comput. Syst. 4(2), 177–187 (2022).
 https://doi.org/10.1049/ccs2.12043