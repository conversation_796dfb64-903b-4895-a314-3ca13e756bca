Popular Communication
 The International Journal of Media and Culture
 ISSN: 1540-5702 (Print) 1540-5710 (Online) Journal homepage: www.tandfonline.com/journals/hppc20
 Organizing music, organizing gender: algorithmic
 culture and Spotify recommendations
 <PERSON>
 To cite this article: <PERSON> (2020) Organizing music, organizing gender: algorithmic
 culture and Spotify recommendations, Popular Communication, 18:1, 78-90, DOI:
 10.1080/15405702.2020.1715980
 To link to this article:  https://doi.org/10.1080/15405702.2020.1715980
 © 2020 The Author(s). Published with
 license by Taylor & Francis Group, LLC.
 Published online: 17 Jan 2020.
 Submit your article to this journal 
Article views: 35693
 View related articles 
View Crossmark data
 Citing articles: 37 View citing articles 
Full Terms & Conditions of access and use can be found at
 https://www.tandfonline.com/action/journalInformation?journalCode=hppc20
POPULAR COMMUNICATION
 2020, VOL. 18, NO. 1, 78–90
 https://doi.org/10.1080/15405702.2020.1715980
 Organizing music, organizing gender: algorithmic culture and
 Spotify recommendations
 Ann <PERSON> of Gender Studies, Södertörn University, Huddinge, Sweden
 ABSTRACT
 Spotify is self-reporting to have 232 million monthly active users in
 July 2019, including 108 million paying subscribers. Often naturalized
 by listeners as a mere window into great collections of music, Spotify
 is an intricate network of music recommendations governed by
 algorithms, displayed as a visual interface of photos, text, clickable
 links, and graphics. With the aim to analyze how three Spotify func
tions, related artists, discover, and browse, organize and represent
 gender while organizing and representing music Spotify is here
 investigated through empirical material collected in qualitative online
 ethnographic studies during 2013–2015. The article problematizes
 how music is organized in algorithmic culture and uncovers gender
ing that can ensue as a result of the service’s recommendation
 algorithms: creating closer circles for music consumption, and orga
nizing music by similarities in genre and gender.
 ARTICLE HISTORY
 Received 4 December 2018
 Revised 11 December 2019
 Accepted 10 January 2020
 KEYWORDS
 Music; gender; genre;
 algorithmic culture;
 streaming services; Spotify
 Finding new music and listening to music is a mediated process, whether we hear music
 on our stereo at home, in the supermarket, watch it on YouTube or in a live performance.
 Since media and media technology is indispensable in music listening it is constantly co
creating the experience of music (Taylor, 2001; Warner, 2003). While not always recog
nized as important for music listening, software is playing a central part of any compu
terized type of media output since web 2.0 (Chun, 2011). Scholars have labeled the
 marketing of cultural commodity like films, books, and music governed by software
 recommendations “algorithmic culture” (Galloway, 2006). Companies recommending
 books, like Amazon, films etcetera, like Netflix, or just any cultural commodity, like
 Google, are increasingly important in creating ideas about what cultural content is
 valuable and meaningful. Striphas (2015) has argued that value ascribed to culture is
 today determined by code hidden from us, owned by private companies. Music streaming
 services, like Spotify, are heavily relying on software algorithms protected by property
 rights to order and display its content. In this article, the software of Spotify is investigated
 through its effects, that is how content is organized to meet the listener by the functions
 related artists, discover and browse.1 The Irish folk-rock artist Damien Rice serves as an
 example to enter the recommendation system of Spotify's algorithms.
 Since software is integral for music listening today, one may wonder how software shapes
 the presentation of music in terms of power dimensions such as gender, sexuality, race/
 ethnicity, class, and nationality. Braidotti (2003, p. 61) argues that gender is a material
 CONTACTAnnWerner <EMAIL> DepartmentofGenderStudies,SödertörnUniversity, 14189 Huddinge, Sweden
 © 2020 The Author(s). Published with license by Taylor & Francis Group, LLC.
 This is an OpenAccessarticle distributedunder thetermsofthe Creative Commons AttributionLicense(http://creativecommons.org/licenses/by/4.0/),
 which permits unrestricted use, distribution, and reproduction in any medium, provided the original work is properly cited.
POPULAR COMMUNICATION 79
 experience and a symbolic construct. Gender organizes social and cultural life and is both felt
 and represented symbolically. According to her, the separation of the material from the
 symbolic and identity from society is false. Thus, feeling like a woman is not separable from
 ideas about femininity circulating at a certain time and place. Collins (1998, p. 63) has argued
 that gender is not distinct from other systems of oppression, like race and class. In fact, these
 systems articulate each other. When investigating the organization and representation of
 music as gendered it is therefore important to observe how gender intersects with for example
 race. Observing difference and same-ness is intrinsic to analyzing gendered culture. Braidotti
 (2003, p. 44) argues that ideas about difference within and between subjects are at the core of
 “gender” in culture and society. When difference is constituted between feminine and mascu
line so is similarity (between masculine and masculine). Gender as binary is central for
 meaning-making, according to Braidotti, even though binaries always are contested
 (Braidotti, 2003,p.44).
 Regarding material and symbolic dimensions of gender, feminist scholars in science
 and technology studies have studied how technologies are gendered. Technology intersects
 with the social and cultural formations of gender, in everyday use of technologies as well
 as processes of technological education and development of new technology (Wajcman,
 2010, p. 144). Wajcman writes that in such processes “(n)either gender or technology are
 pre-existing” (Wajcman, 2010, p. 144). Here, the relationship between technology and
 gender as meaningful, and co-constituting is understood as involving multiple axes of
 power. Software, as a technology, is being co-constituted with gender, race, etcetera.
 Within a similar theoretical framework, McNeil (2007, p. 127) has argued that the
 masculinization of technology is an idea and practice not only evident in machines and
 institutions but also in symbols, language, and identity practices surrounding socio
technological relations. While McNeil, like Collins and Wajcman, does not see gender
 as an isolated power dimension she argues that the way technology is repeatedly mascu
linized is shaping technology as strongly gendered.
 More specifically working on streaming software Eriksson and Johansson (2017, p. 177)
 has concluded that Spotify recommendations are dominated by artists labeled male in all
 genres and hereby furthers male-as-norm in the music industry. Their study shows that
 software is shaping socio-technological relations of gender for their users, making music
 streaming services worth discussing as technologies of difference in music today.
 The aim of this article is to analyze how three Spotify functions, related artists, discover,
 and browse, organize and represent gender while organizing and representing music.
 Thus, how the software technology of the service genders the interface of music con
sumption. When the material for this article was collected related artists, discover, and
 browse were three prominent functions (Johansson, Werner, Åker, & Goldenzwaig, 2017).
 Drawing on a material of screenshots of particular pages and artists, and field notes taken
 while listening to Spotify on a daily basis, the article discusses how Spotify genders music.
 And how it builds on ideas of difference already existing in genre and artist representa
tions of pre-streaming popular music culture.
 These aspects are studied along three lines of inquiry: First the methodology, larger
 study, and context are presented. Then, critical analysis of music streaming and algo
rithms as well as current feminist studies of online media software are discussed as two
 research contexts shaping the analysis at hand. Next, the analysis is divided into three
 sections drawing on three Spotify functions, each deepening the understanding of how
80 A.WERNER
 gendered software recommendations structure the interface on Spotify. And finally, two
 concluding sections focus on gendered music streaming as part of algorithmic culture.
 Material and method
 The material analyzed here is part of a larger study aiming to understand music use
 online. The material consisted of focus groups with 80 young adults in Moscow and
 Stockholm, and material from their favorite platforms. The platforms studied were
 Spotify, YouTube, and VKontakte. The focus groups were conducted in 2012 and 2013,
 while material was collected online between February 2013 and March 2015 in order to
 map the networks and the possibilities provided for online music (Johansson et al., 2017).
 The contexts of this study were two cities and digitalization of music culture. The study
 found that streaming has become an increasingly popular way to listen to, and discover
 music, especially in Stockholm but also in Moscow. In Sweden, Spotify was found to
 dominate music listening among the participants of the focus groups, while in Moscow
 VKontakte was the most popular site for music listening.
 In this article, analysis begins by focusing on how Spotify software organizes a particular
 artist’s network. This artist, Damien Rice, was discussed in some Stockholm focus groups as
 a good artist. Since Spotify adapts to the user by storing previous choices and basing
 suggestions on them it is hard to know what parts of the service are core-features, presented
 to all users, and what parts are individually adapted or location specific. To an unknown
 extent, recommendations discussed here have been adapted for me, based on my previous
 listening habits on Spotify. I joined Spotify as a paying subscriber in 2012 for research
 purposes but had been a nonpaying listener since 2009. Besides entry values such as where
 I live and my gender, what friends I had (Spotify was connected to Facebook in 2012), Spotify
 already had data about my listening habits. Research with bots (Eriksson & Johansson, 2017)
 has shown that Spotify recommendations tend to take on a gendered pattern for the listener
 independent of the given gender of the account holder but that genre listening shapes
 recommendations. Spotify is personalized and the organization of recommendations and
 relations between artists are based on accumulated choices of other users, thus, trying to find
 a “true” Spotify structure is impossible.
 Mapping the logics of web sites through screenshots has been discussed as fruitful for
 digital methods by Moore (2014, p. 142) who argues that screenshots can be important
 analytical entities. Screenshots would not have been possible to analyze without the context
 of continuous use of Spotify over a period of time. The researchers' experiences were
 documented in field notes, common in online ethnography (Hine, 2015, p. 89; Werner,
 2015). This article relies on these two groups of materials together with findings in the
 focus groups. The analysis is situated in contemporary studies of algorithms and software.
 Algorithmic culture
 Investigating software for music streaming Morris (2015a, p. 178) has argued that the
 business model of music streaming services relies on libraries of music being nontransferable
 to other platforms, effectively making one’s record collection owned and organized by
 a commercial company. This, he claims, presents a game-changer for music culture.
 Software applications for music streaming change the ownership and durability of music
POPULAR COMMUNICATION 81
 listening, now highly vulnerable to the companies’ policy development and commercial
 success. Further, he argues, recommendation systems give companies the power to determine
 the value of music as the new intermediaries of music (Morris, 2015b), gatekeepers of taste.
 Streaming services are central players in algorithmic culture, where cultural value is deter
mined by processes partly human, partly machine. Mackenzie (2017, p. 8) argues that critical
 investigation of what commercial algorithms do has become a new strand of cultural theory
 devoted to machine learners. In an era where software is learning from experience, con
structing complex systems functioning like black boxes (Mackenzie, 2017;Parisi,2013) the
 logic behind recommendations on Spotify is difficult to fully know. Digital media is providing
 cultural content for streaming– for example watching and listening– but discoverability of
 content is heavily reliant on recommendations and search engines. These organize the large
 amount of content on Amazon, Netfix, and Spotify. Algorithmic forms of ordering cultural
 content has been labeled algorithmic culture, when algorithms decide ordering, visibility, and
 value ascribed to cultural commodity (Galloway, 2006; Hallinan & Striphas, 2016). Striphas
 (2015, p. 407) concludes that algorithmic culture presents itself as the effect of democratic
 processes, as if it is promoting culture consumed by many. While it is really private forces
companies– that are deciding the value of culture through software processes that remain
 hidden to us. Thus, algorithmic culture reduces decisions on taste to a few actors defining
 what is good and for whom, constructing social groups and cultural value in the process.
 In studies of algorithms, it is relevant to ask: what combinations of software, hardware,
 and actors are encouraged and what does it mean? Someone accessing a website or service
 from a Swedish IP address is automatically assumed to understand Swedish and be
 interested in things Swedish, enforcing ideas on Swedish-ness in the process. Big data is
 not meaningful without the uses of it, as Brayne (2017) shows in her analysis of how police
 use big data in surveillance. She argues that the preexisting ideas that form what data is
 collected, for example who is considered a “risk individual” (Brayne, 2017, p. 986), are
 integrated into the effects of the data: who is surveilled and who is not. Assumptions about
 risk or more mundane things like national identity, language, and taste are not the only
 ideas algorithms speak to (and build on): through data mining users are mapped and
 approached accordingly in content as well as advertisement. The role of gendering in these
 processes has been studied but deserves more attention.
 Feminist studies of software and social media
 Linking software to power dimensions, Chun (2011, p. 2) has argued that software is
 omnipresent in new media but also impossible to map since software processes are
 infinite, and difficult to conceive, because of their invisibility to the user. Softwares are
 understood by Chun as mediums of power since they provide one way to navigate
 a complex machine world. The navigation software provides is not self-evident or neutral
 but based on choices.
 Onewaythatsoftware helps the user navigate is by ordering identities in systems of gender
 and race. Nakamura (2002) argues that online gendered and raced identities often are
 presented as “menus” to choose from. At the time of her research, such choices were often
 visible in the interface in games or chatrooms and after the user made their choices the
 software used them to present the user with some options and not others. Nakamura
 concludes that identities are perceived as clear-cut choices in this type of software, man/
82 A.WERNER
 woman,white/black/Asian, making it impossible to represent the fluidity of everyday identity,
 identifying for example as both Asian andblack,or neithermanorwoman.Identityconsisting
 of one gender, one race, one hometown, is what the software built on to understand users.
 Software’s way of coding identity selections was integral in the shaping of web 2.0 (Nakamura,
 2002). These choices havestayed oninthe development ofsocial mediaeventhoughtheymay
 be less visible. McNicol (2013, p. 205) states that gendered choices are inscribed differently in
 social media systems, choices are more or less required by different services and the options
 are formulated differently on social media like Facebook, Twitter, and Diaspora. Therefore,
 the effects of gendering on social media can also be different. Bivens (2015) has investigated
 Facebook software paying interest to how Facebook is organized as gender binary. She has
 argued that one effect of the algorithm people you may know on Facebook is that it puts
 survivors of sexual harassment and violence in contact with their perpetrators, possibly
 prolonging the trauma. Paying attention to gendering effects of software in social media
 Bivens (2015, p. 715) argues that social media software is ripe for feminist analysis, and that
 social media software is deeper manifestations of power than the surface content. In her
 research on Facebook Bivens (2015, 2017) investigates how software enacts normalizing
 logics, for example by offering several choices for gender identity while still hiding a binary
 division of users in terms of gender in the original code.
 The secrecy may also be analyzed as part of the meaning of software (Chun, 2011, p. 5).
 The interface is presented as neutral, normal, when in fact it is the result of a series of
 choices made with purposes that are hidden and assuming difference in terms of identity.
 Bishop (2018, p. 81) has argued that the algorithm of YouTube rewards normative white
 femininity in beauty vloggers by recommending it more often, in line with desires of
 brands and advertisers who deem this femininity as more marketable. It does so by the
 choices made by the algorithms, notably what videos are suggested to viewers watching
 beauty vloggers.
 Three functions
 On Spotify, music listeners can make playlists, save favorite artists, and albums in their
 archive called my music and listen to radio channels, or playlists put together by
 themselves, other listeners, or by Spotify. In order to discuss how recommendations
 are structured as socio-technological, and as such building on and furthering some
 ideas of gender intersecting with race and nationality, I will address three functions on
 Spotify. The functions are: related artists that lists artists understood as similar to the
 one you are already listening to, discover that suggests personalized music based on
 previous listening and what Spotify deems similar to that listening, and the first page
 called browse that assembles Spotify’s most important features.2 This selective method
 of approaching Spotify through functions was motivated by the central place of these
 functions at the time of the study and how they were discussed in focus groups.
 Who is kin? The related artists function
 On Spotify, at the time of the study, the related artists function suggested artists presented
 as similar to the one you were currently listening to. While all three functions analyzed in
 this article were modified during 2013–2015 the main principle of them remained intact.
POPULAR COMMUNICATION 83
 In some ways, they stay important for Spotify in 2019 even though they are labeled
 differently (related artists is now called fans also like) and their placement in the interface
 differs (browse has been moved, discover is now discover weekly). The idea that music is
 connected to other music is the core of Spotify’s ordering of music through recommenda
tions in related artists and discover. In the first year of material collection, I listened to
 Irish folk-rock singer Damien Rice– because he was popular among the participants in
 the Stockholm focus groups. When I first opened Damien Rice’s profile I noticed that all
 the artists presented by related artists were white-ish men– not a noteworthy or unusual
 observation in itself. Feminist music scholars have concluded that the genre rock is
 masculinized, white washed, and also dominant on the western popular music market
 and in music journalism (Coates, 1998; Leonard, 2007). The most popular rock artists are
 often presented as white men, solo artists or in groups, even though the type of mascu
linity they feature varies in different types of rock, from metal to folk. Artists identified as
 women, often but not always representing femininity, are also present, but often marginal
 in rock. Music genres interpreted as masculine, like rock, have often been considered of
 higher value and more authentic than feminized popular music genres like pop (Biddle &
 Jarman-Ivens, 2007, p. 3).
 The idea that some artists were related intrigued me, the word “related” implies
 kinship, kinship in turn is often imagined in terms of gender, nation, and race. And
 while white rock music seldom is described as white, it can be labeled as national, like for
 example British rock and pop music (Stratton, 2010, p. 27, see also Zubeeri, 2001).
 Stratton (2010) argues that when calling music “British” there is an underlying assumption
 that British means white– if a group or artist is not white this has to be pointed out.
 Ascribing music to a certan nation also usually takes the sounds of that music into
 account. Music may be percieved as sounding British, like brit pop, or not, like bhangra,
 both genres are produced in the UK but understood differently in terms of nation and
 race. Artists that are seen as related by Spotify may therefore be so– in terms of sharing
 group identities based on gender, nation, class, and race. The white folk-rock men
 connected to Damien Rice by Spotify caught my interest and I started keeping track of
 the network of artists unfolding with Damien Rice as starting point. He will be used as an
 example to discuss the related artists function.
 I concluded that Damien Rice was presented as male by Spotify because he was referred
 to as a he, and so were most artists suggested by related artists on his page. Artists not
 referred to as he or she are uncommon on Spotify, still, in cases such as popular
 transgender artists like Antony Hegarty (of Antony and the Johnsons) Spotify refers to
 Antony by name, not using any pronoun in the text describing them. Spotify operates with
 a binary idea of gender while recognizing some openings for alternatives outside the
 binary, without naming them. Race was not explicitly addressed in the descriptions of the
 majority of artists related to Damien Rice. And since they appeared white in their photos
 not addressing whiteness reinforces white as an invisible norm, reinforcing the idea that
 white people are “outside” of race, and race belongs to the other. Sometimes nationality
 was used to describe artists, “Irish”, or “Czech” (while Tracy Chapman was “African
American”, a racial signifier), and their nationality could, as Stratton (2010) argues, be
 seen to contain a racial presentation, Irish implies white and Irish unless some other
 feature is presented. Sometimes class was mentioned in terms of background, when artists
 were described as having a “working class background”.
84 A.WERNER
 In the first months of 2013, I followed Damien Rice’s related artists around by mapping their
 (in turn) relations in three steps and observing genre affiliations as well as representations of
 gender, race, and nationality in their pictures, names, and biographies. I also listened to their
 music, even though this article does not focus on the in-music qualities of sounds and lyrics. My
 mapping showed that predominantly male artists, predominantly white artists, predominantly
 singer-songwriter and folk-rock artists and predominantly solo artists were related to Damien
 Rice within three steps of recommendations.3 Musically a slow tempo folk-rock featuring
 acoustic elements and emotional lyrics (often about romantic love and loss) in the English
 language dominated. The style of most popular songs of these artists contributed to presenting
 a brooding, emotional, and sensitive yet strong masculinity easily recognizable from singer
songwriter and folk-rock genre tradition. The nationalities of the artists related to Damien Rice
 were mainly European or North American. The age and generation of the artists varied: older
 artists not actively writing music anymore like Bob Dylan and Nick Drake appeared, so did Glen
 Hansard, The Frames and David Gray and even though there was an Irish white dominance
 a Swedish artist with parents from Argentina, José Gonzales, and a female African-American
 artist, Tracy Chapman could both be found in the network (they were presented in this way).
 The artists considered related to Damien Rice by Spotify were partly changing over time, while
 the overall impression of genre, nationality, race, and gender in the group stayed the same, and
 some artists, like Glen Hansard, were always in the first step after Damien Rice.
 Later on in the study, in March 2015, there was one female white solo artist in the top
 four related artists to Damien Rice: Lisa Hannigan who is Damien Rice’s ex-girlfriend. In
 February 2013, there was one artist presented as female in the top five related artists to
 Damien Rice: Marketa Irglova (one half of The Swell Season, a group also within the
 network). Marketa Irglova performed alongside Glen Hansard in the popular Irish movie
 Once: portraying love and Irish folk-rock music. Apart from these two women, Tracy
 Chapman, and some Irish folk-rock bands with male and female members, very few
 female artists appeared within three steps of Damien Rice during the years of study,
 even fewer black or brown artists appeared (only the two artists already mentioned). The
 related artists function is according to an interview with Spotify founder Daniel Ek (Gelin,
 2012) based on accumulation of choices that is fed back to listeners. This is a common
 practice in algorithmic culture, and his explanation does not give away what choices the
 algorithms are making. The software appears neutral, and is unknown, but based on
 multiple choices fed into preexisting number of programming presumptions. As Striphas
 (2015) has argued the recommendations do not only mirror accumulation of choices, it
 builds on the companies definitions of commodities, social groups, and categorizations of
 both, genre being important on Spotify.
 In the focus group, study participants described the related artists function as a good
 way to discover new music. Finding new good things to listen to is every music lover’s
 dream: therefore, the related artists function as such shapes the use of Spotify. In the case
 of Damien Rice, the function promoted choices that were similar to Damien Rice in terms
 of how genre, gender, and race were presented. The latter two categories take shape
 through the genre category. Genre rules associate the style of folk-rock Damien Rice
 performs with Irish, British, and North American nationality, whiteness, and masculinity.
 But there are many more female (mostly white) folk-rock singer songwriters that do not
 appear around Damien Rice, thus genre as explanation does not fully hold up. As Striphas
 (2015) has argued algorithmic culture hides an elite system of unknown choices made by
POPULAR COMMUNICATION 85
 algorithms behind the presumption that the “users” are in charge of culture through the
 accumulation of choices. The presentation of recommendations and top choices in
 cultural consumption presents ideas about cultural value, as well as of social groups
 (Striphas, 2015, p. 406). In related artists, cultural value around Damien Rice is white
 and male, with few exceptions. And the female artists often suggested by related artists
 (Lisa Hannigan and Marketa Irglova) are also presented as romantic partners to Damien
 Rice in real life and Glen Hansard on screen, rather than successful on their own terms.
 Who are you? The discover function
 The discover function presented a list of personalized suggestions, you could at the time of
 study find it on the browse page, click on it and enter personalized suggestions. It
 recommended artists and songs to listen to, based on previous account activity, the
 collected habits of all listeners and constructed ideas about music similar to the one you
 liked (thus seemed to build on related artists). As discover is personalized– adapted to
 previous patterns on the account– and related artists seem to be based on national user
 habits (Gelin, 2012), my use of Spotify at the time was providing a unique interface. In
 short, based on my listening habits, nobody else would get the exact same suggestion
 pattern as I did. On the other hand, the pattern displays the network of artists and genres
 that Spotify recommendations build on and some of the re-occurring principles of the
 algorithms. Thus, while geographical points of reference and my already existing choices
 are particular the logics of discover display algorithms all listeners meet.
 Discover implies, by name alone, that there is a possibility for the listener to discover new
 music. But the suggestions made to me during the period of the study were rarely artists that
 were unknown to me. Suggestions like “You listened to Beyoncé maybe you would like Brandy”
 can almost seem to mock the listener, and discover rarely led me to discover artists I did not
 know. In order to experiment with the discover function, I listed to Spotify every day, according
 to my owntaste in periods and to particular artists and genres I would not normally listen to in
 other periods. During the autumn of 2014, I listened to a large amount of K-pop, mostly groups
 with several female members. Within a day discover suggested young South Korean female pop
 artists back to me, and a few young men. The artists Spotify recommended to me were popular
 chart successes in South Korea, but not all of the songs were recent releases, this can be
 understood as a result of my national position, I might have gotten more up-to-date suggestions
 had I been on a South-Korean IP address since national listening patterns are used in the
 recommendation algorithms. Accordingly, when I stopped listening to K-pop the suggestions in
 discover changed. Thus, discover was in 2014 a very temporally sensitive function picking up on
 recent listening habits. Earlier on in 2013 discover would sometimes suggest music that I had not
 listened to in a long time, also telling me so “you have not listened to x in a while”.This
 highlights that Spotify had been tuning the algorithm during the period: experimenting to find
 the now-ness of recommendations. Being able to use surveillance knowledge in real-time is
 a consequence of big data (Brayne, 2017,p.991).Itiseasytomonitoracertainperiod,andthe
 present is often seen as more relevant than the past. While related artists was a function
 producingpatternsthatdidnotchangethat much over time (around Damien Rice) discover
 was time sensitive and picked up on recent habits of the individual listener. While this
 distinguished discover from related artists similarity in genre, gender, and race was recom
mended by both functions.
86 A.WERNER
 We are Spotify. The browse function
 The first page of Spotify was called browse and contained a banner with announcements on
 top, and then content consisting of an overview of popular playlists, new releases, news, top
 lists, genres, moods, and the discover function. Browse promoted the main guiding functions,
 highlighting some playlists, new releases,andgenres. Opposed to early YouTube’s centrality of
 the search function, Spotify aimed to help the listener by arranging the music like in a record
 store (but Spotify has a search function too). Fore fronting some genres and new releases
 remediated the organization of a physical record store. The playlist was inspired by early
 iTunes, and the discover function can be seen as being based on Pandora’s business idea
 created in 2000, aiming to find the next similar (but not too similar) song. Avdeeff (2012)
 concludes that personalized music consumption has grown with digital music use. Her
 research shows the ease with which a listener can personalize one’s listening, picking the
 next artist or song on the go, thelistener understands this as an advantage of digital music use:
 having a record collection to choose from inone’spocket.Browse was a function that aimedto
 present and organize this collection of music for the listener.
 Browse was clearly shaped by the day and time one entered it: by advertising playlists like
 “Morning commute” in the morning and “New music Friday” on Fridays, and by the news
 (Avicii releases a new single on so-and-so date). The top advertisement banner took up a lot of
 space in browse and often presented a new albumor playlist. All of these ways of guiding the user
 can be read as aiming to create an experience of time. Browse presented Spotify as a place within
 time. It strived to be part of the listeners' everyday rhythm and up-to-date (see Johansson et al.,
 2017, chapter 2). While browse differed between countries, by presenting popularity of songs
 divided by countries under top lists, it seemed to appear the same for all users in one country
 (except in discover). Browse did not order suggestions by similarity. It presented a variety of
 artists and genres, while at the same time still portraying ideas of genre, gender, and race. For
 example: the genre soul was during the study represented by a logo shaped as a woman’shead
with afro hairstyle and hoop earrings implying a black woman– while rock was represented by
 an amplifier. Music genres are established by rules that are based on social understandings
 (Fabbri, 1981), and as such not given by musical sound or style only. Still, stylistic components
 in the music do play a part in genre classification. Genre groupings also often hinge on for
 example nation, race, class, gender, and sexuality (Brackett, 2016,pp.3–4). To illustrate playlists
 found under the link to the genres soul and rock photos were used. The top playlists under the
 genresoulwereillustratedwithAfrican-American artists, often men and women smiling, while
 the top playlists under the genre rock were illustrated with guitars, empty houses, and white
 men. Ordering and representation were significant for how Spotify portrayed music for the
 listener entering through browse,eventhoughbrowse displayed different routes available. The
 routes the listener could take were often illustrated visually through ideas about different social
 groups representing different music genres. Further, most choices Spotify’ssoftwaremadewhen
 targeting the Swedish audience on browse through news promoted Swedish artists and artists
 from the English-speaking world. National diversity was not promoted by browse. There was
 a mainstream dominance promoting pop, rock, and EDM but the artists recommended were
 not necessarily only mega stars of chart pop. The promotions can be assumed to mirror
 expectations of the Swedish Spotify listeners’ habits by A & R and record companies. Without
 being sure about how the software selects, and in what pool of different choices the selections are
 made, browse built an idea of Swedish taste and created patterns of listening, by urging listeners
POPULAR COMMUNICATION 87
 to notice for example Avicii’s new single. Both the invisible algorithmic choices, and the visible
 interface design and its portrayal of music and artists nudged the listener in different directions
 according to taste and identity. Still, the logic of browse was more diversified than the two
 functions previously discussed and it seemed that browse aimed to speak to multiple (Swedish)
 audiences at the same time.
 Gendered music streaming
 The functions related artists, discover,andbrowse, as has been shown, strengthen ideas about
 genre affiliations of artists and songs. They render presumptions about taste in your nation of
 IP origin important but invisible. They do this by feeding back other listeners’ choices,
 algorithmic choices and choices made on your account as recommendations of new music.
 These recommendations are ordered in terms of similarity, this similarity is musical but also
 include similar representations of gender and race. What possible music listening does this
 create? One answer is that the functions result in a feedback loop: following related artists
 from Damien Rice led me back to Damien Rice every step of the way. The use of genre and
 previous choices as cornerstones in Spotify’s construction of similarity are here found to
 reinforce connections between artists similar in terms of gender and race. The recommenda
tion functions on Spotify make similarity central and in that process they emphasize differ
ence. WhenTracyChapmanappearsasrelatedtoDamienRiceshe reallystandsout asa black
 woman.Whilegenrescanbebroad,relatedartistscreatesnetworkswhere mostartists arevery
 similar to the first artist of choice. For example: Rihanna leads you to the top-related artist
 Beyoncé, who leads you to Destiny’s child, bringing you to Kelly Rowland who leads to Ciara
 and Ciara leads you back to Kelly Rowland. No male artist, white artist, and no artist from
 another generation, nation, or genre are included in this circle of recommendations.
 The recommendations of Spotify reflect patterns already known, premieres very famous
 artists and rarely gives surprising advice. Also, since the recommendation system is accumulated
 over time few debuting artists will appear though related artists or discover,andneitherwillold
 forgotten artists.4 Rose (1994) has argued, in relation to hip hop, that musical genres can be
 racialized in a politically progressive way, fighting racism, and simultaneously contain sexist
 messages fixing gendered racial stereotypes. Hence, gendering and racialization of genres may
 have progressive potential and essentializing dangers at the same time. What can be observed in
 Spotify’s functions is that genres become narrowly gendered through related artists and discover.
 InarecordstoreBeyoncéorCiaramightbeplaced next to a male artist within mainstream R&B,
 or a not so popular artist– they would still likely be categorized as part of an African-American
 popular music tradition– but on Spotifyrelated artists lists them next to young African
American top-selling females. Participants in focus groups argued that they could find immense
 amounts of music through the Internet, and researchers have pointed out a heightened possi
bility for musical eclecticism supported by digital music formats (Avdeeff, 2012). But, it seems
 doubtful that the software of contemporary streaming services supports these practices. While
 listeners may perceive Spotify as liberating, and use it in such ways, the functions analyzed here
 limit listening patterns by building gendered and racialized connections between artists and
 genres. Users of Spotify may still listen to a variety of music that they find out about for example
 through other media, through advice from friends or by going to live performances. Also, they
 mayactivelyusethesearchfunctiontofind artists or explore different genres starting through
 browse. But this is not what Spotify invites them to do.
88 A.WERNER
 Conclusion
 The business idea of companies selling streamed music has been scrutinized for its capitalist
 logics (Morris, 2015a, 2015b). Claims are that algorithmic culture hides how companies shape
 cultural value and social groups through the algorithms while presenting them as “choices” of
 audiences (Striphas, 2015). By promoting similarity, emphasizing what is popular right now
 andbuilding onthe genresystem, Spotify’s network of music aims tosimplifylistening. At the
 same time, Spotify organizes gender, nationality, and race in music culture. This is done
 materially by connections in the interface, and discursively by representations of artists and
 genres. While harmless on the surface, when considered more closely Spotify’s functions for
 recommendations help reconstruct dominating genres like rock as male-focused, masculine,
 and white. This gendering of rock has material implications in listening experiences and
 further a masculine rock discourse. While the aim of Spotify may be to personalize listening
 and guide their users to music they will love, the result is enhancing the already existing
 gendering of popular music genres. Acknowledging that popular music genres are shaped by
 nation, race, gender, etcetera (Brackett, 2016), they also articulate historical injustices such as
 colonialism, slavery, sexism and oppression of women, and violence against transgender
 persons. The organizing and representation of musical taste and social groups on Spotify
 are thus not coincidental, or innocent, but reinforcing patterns of power.
 Notes
 1. Empirical material discussed here was collected within the research project “Music use in the
 online media age,” 2012–2015, funded by Riksbankens Jubileumsfond, and conducted with
 Sofia Johansson, Patrik Åker and Gregory Goldenzwaig. The project conducted focus groups
 and online ethnography (on VKontakte, Spotify, and YouTube) mapping the meaning of music
 online. The main results are discussed in Streaming Music (Johansson et al., 2017).
 2. The functions described here mirrors Spotify in 2014–2015 as it looks in a computer interface
 for paying subscribers. The material about the related artists function was collected over
 a longer period of time, starting in February 2013.
 3. Male artists dominate popular music (Smith et al., 2019) and most studies put women/others at
 around 20% among performing artists. The argument here is not that algorithmic culture
 distorts reality (or not). How algorithmic culture is gendered and what is presented as
 recommended music is in focus.
 4. Debuting artists may appear on browse.
 Disclosure statement
 No potential conflict of interest was reported by the author.
 Notes on contributor
 Ann Werner is Associate Professor in Gender Studies at Södertörn University in Sweden. She has
 written on the subject gender, popular music and media technology within a variety of articles and
 books including the co-authored monograph Streaming Music (Routledge 2017 with Sofia
 Johansson, Patrik Åker and Gregory Goldenzwaig) and her recent article “What does gender
 have to do with music anyway?” (Per Musi 2019). She was on the executive committee of the
 International Association for the Study of Popular Music 2015-2019.
POPULAR COMMUNICATION 89
 References
 Avdeeff,M. (2012). Technological engagement and musical eclecticism: An examination of con
temporary listening practices. Participations: Journal of Audience and Reception Studies, 9(2),
 265–285.
 Biddle, I., & Jarman-Ivens, F. (2007). Introduction. Oh boy! Making masculinity in popular music.
 In F. Jarman-Ivens (Ed.), Oh boy! Masculinities and popular music (pp. 1–20). New York:
 Routledge.
 Bishop, S. (2018). Anxiety, panic and self-optimization: Inequalities and the YouTube algorithm.
 Convergence, 24(1), 69–84. doi:10.1177/1354856517736978
 Bivens, R. (2015). Under the hood: The software in your feminist approach. Feminist Media Studies,
 15(4), 714–717. doi:10.1080/14680777.2015.1053717
 Bivens, R. (2017). The gender binary will not be deprogrammed: Ten years of coding gender on
 Facebook. New Media & Society, 19(6), 880–898. doi:10.1177/1461444815621527
 Brackett, D. (2016). Cathegorizing sound: Genre and twentieth-century popular music. Oakland:
 University of California Press.
 Braidotti, R. (2003). Becoming woman: Or sexual difference revisited. Theory, Culture & Society, 20
 (3), 43–64. doi:10.1177/02632764030203004
 Brayne, S. (2017). Big data surveillance: The case of policing. American Sociological Review, 82(5),
 1–32. doi:10.1177/0003122417725865
 Chun, W. H. K. (2011). Programmed visions: Software and memory. Cambridge, MA: MIT Press.
 Coates, N. (1998). Can’t we just talk about music: Rock and gender on the Internet. In T. Swiss,
 J. Sloop, & A. Herman (Eds.), Mapping the beat: Popular music and contemporary theory (pp.
 77–99). Malden, Mass: Blackwell.
 Collins, P. H. (1998). It’s all in the family: Intersections of gender, race and nation. Hypatia, 13(3),
 62–82. doi:10.1111/j.1527-2001.1998.tb01370.x
 Eriksson, M., & Johansson, A. (2017). Tracking gendered streams. Culture Unbound, 9(2), 163–183.
 doi:10.3384/cu.2000.1525.1792163
 Fabbri, F. (1981). A theory of musical genre: Two applications. In D. Horn & P. Tagg (Eds.),
 Popular Music Perspectives (pp. 52–81). Göteborg and Exeter: International Association for the
 Study of Popular Music.
 Galloway, A. R. (2006). Gaming: Essays on algorithmic culture. Minneapolis: University of
 Minnesota Press.
 Gelin, M. (2012, December 2). Så bygger Daniel Ek musikens framtid. Dagens Nyheter, K12. (This is
 how Daniel Ek builds the future of music).
 Hallinan, B., & Striphas, T. (2016). Recommended for you: The Netflix prize and the production of
 algorithmic culture. New Media and Society, 18(1), 117–137. doi:10.1177/1461444814538646
 Hine, C. (2015). Ethnography for the Internet: Embedded, embodied and everyday. London:
 Bloomsbury Academic.
 Johansson, S., Werner, A., Åker, P., & Goldenzwaig, G. (2017). Streaming music: Practices, media,
 cultures. Abingdon, Oxon: Routledge.
 Leonard, M. (2007). Gender in the music industry: Rock, discourse and girl power. Aldershot:
 Ashgate.
 Mackenzie, A. (2017). Machine learners: Archeology of a data practice. Boston, MA: MIT Press.
 McNeil, M. (2007). Feminist cultural studies of science and technology. London: Routledge.
 McNicol, A. (2013). None of your business? Analyzing the legitimacy and effects of gendering social
 spaces through system design. In G. Lovink (Ed.), Unlike us: Social media monopolies and their
 alternatives (pp. 200–219). Amsterdam: Institute of Network Cultures.
 Moore, C. (2014). Screenshots as virtual photography: Cybernetics, remediation, and affect. In
 P. L. Arthur & K. Bode (Eds.), Advancing digital humanities: Research, methods, theories (pp.
 141–160). Basingstoke: Palgrave Macmillan.
 Morris, J. W. (2015a). Selling digital music: Formatting culture. Oakland: University of California
 Press.
90 A.WERNER
 Morris, J. W. (2015b). Curation by code: Infomediaries and the data mining of taste. European
 Journal of Cultural Studies, 18(4–5), 446–463. doi:10.1177/1367549415577387
 Nakamura, L. (2002). Cybertypes: Race, ethnicity and identity on the Internet. New York, NY:
 Routledge.
 Parisi, L. (2013). Contagious architecture: Computation, aesthetics and space. Cambridge, Mass: The
 MIT Press.
 Rose, T. (1994). Black noise: Rap music and black culture in contemporary America. Hanover, N. H.:
 Wesleyan University Press.
 Smith, S. L., Choueiti, M., Pieper, K., Clark, H., Case, A., & Villanueva, S. (2019). Inclusion in the
 recording studio? Gender and race/ethnicity of artists, songwriters and producers across 700
 popular songs from 2012-2018. Los Angeles, California: USC Annenberg inclusion initiative.
 Stratton, J. (2010). Skiffle, variety and Englishness. In A. Bennett & J. Stratton (Eds.), Britpop and
 the English music tradition (pp. 27–40). Farnham: Ashgate.
 Striphas, T. (2015). Algorithmic culture. European Journal of Cultural Studies, 18(4–5), 395–412.
 doi:10.1177/1367549415577392
 Taylor, T. D. (2001). Strange sounds: Music, technology and culture. New York, NY: Routledge.
 Wajcman, J. (2010). Feminist theories of technology. Cambridge Journal of Economics, (2010(34),
 143–152. doi:10.1093/cje/ben057
 Warner, T. (2003). Pop music, technology and creativity: Trevor Horn and the digital revolution.
 Aldershot: Ashgate.
 Werner, A. (2015). Moving forward: A feminist analysis of mobile music streaming. Culture
 Unbound, 7(2), 197–213. doi:10.3384/cu.2000.1525.1572197
 Zubeeri, N. (2001). Sounds English: Transnational popular music. Urbana, Ill.: University of Illinois
 Press.